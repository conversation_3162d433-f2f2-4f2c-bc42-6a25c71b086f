import React, { useState, useEffect } from 'react';
import { Card, Progress, Steps, Button, Modal, Tabs } from 'antd';
import { LoadingOutlined, CheckOutlined, CloseOutlined, EyeOutlined } from '@ant-design/icons';
import type { ProcessingJob } from '../types';

const { Step } = Steps;
const { TabPane } = Tabs;

interface ProcessingProgressProps {
  job: ProcessingJob;
  onBack: () => void;
  onRetry?: () => void;
}

interface ProcessingStep {
  key: string;
  title: string;
  icon: string;
  description: string;
  progressRange: [number, number];
}

const PROCESSING_STEPS: ProcessingStep[] = [
  { 
    key: 'parse', 
    title: '数据解析', 
    icon: '📊', 
    description: '正在解析和验证输入数据',
    progressRange: [0, 15]
  },
  { 
    key: 'transcribe', 
    title: '语音检测和翻译', 
    icon: '🎤', 
    description: '音频转录或文本处理',
    progressRange: [15, 35]
  },
  { 
    key: 'simplify', 
    title: '关键词提取', 
    icon: '📝', 
    description: '文本简化和关键词分析',
    progressRange: [35, 50]
  },
  { 
    key: 'image', 
    title: '图像生成', 
    icon: '🎨', 
    description: '根据内容生成相关图片',
    progressRange: [50, 70]
  },
  { 
    key: 'speech', 
    title: '语音生成', 
    icon: '🔊', 
    description: '生成高质量语音音频',
    progressRange: [70, 85]
  },
  { 
    key: 'video', 
    title: '视频合成', 
    icon: '🎬', 
    description: '合成最终视频文件',
    progressRange: [85, 95]
  },
  { 
    key: 'complete', 
    title: '任务完成', 
    icon: '✅', 
    description: '处理完成，准备下载',
    progressRange: [95, 100]
  }
];

const ProcessingProgress: React.FC<ProcessingProgressProps> = ({ job, onBack, onRetry }) => {
  const [logs, setLogs] = useState<string[]>([]);
  const [showLogModal, setShowLogModal] = useState(false);
  
  console.log('ProcessingProgress rendering with job:', job);
  
  if (!job) {
    console.log('No job provided to ProcessingProgress');
    return <div style={{ padding: '20px' }}>Loading job data...</div>;
  }

  // 获取当前步骤
  const getCurrentStep = () => {
    const progress = job.progress || 0;
    
    for (let i = 0; i < PROCESSING_STEPS.length; i++) {
      const step = PROCESSING_STEPS[i];
      if (progress >= step.progressRange[0] && progress <= step.progressRange[1]) {
        return i;
      }
    }
    
    if (progress >= 100) return PROCESSING_STEPS.length - 1;
    return 0;
  };

  const currentStepIndex = getCurrentStep();
  const currentStep = PROCESSING_STEPS[currentStepIndex];

  // 获取步骤状态
  const getStepStatus = (stepIndex: number) => {
    const progress = job.progress || 0;
    const step = PROCESSING_STEPS[stepIndex];
    
    if (job.status === 'failed' && stepIndex === currentStepIndex) {
      return 'error';
    }
    
    if (progress >= step.progressRange[1]) {
      return 'finish';
    } else if (progress >= step.progressRange[0]) {
      return 'process';
    } else {
      return 'wait';
    }
  };

  // 获取步骤图标
  const getStepIcon = (stepIndex: number) => {
    const status = getStepStatus(stepIndex);
    
    if (status === 'error') {
      return <CloseOutlined />;
    } else if (status === 'finish') {
      return <CheckOutlined />;
    } else if (status === 'process') {
      return <LoadingOutlined />;
    }
    
    return null;
  };

  // 模拟日志数据
  useEffect(() => {
    const mockLogs = [
      `2025-08-20 ${new Date().toLocaleTimeString()} [初始化] 任务已创建`,
      `2025-08-20 ${new Date().toLocaleTimeString()} [初始化] 开始处理任务`,
      `2025-08-20 ${new Date().toLocaleTimeString()} [数据解析] 开始数据解析`,
      `2025-08-20 ${new Date().toLocaleTimeString()} [数据解析] 开始数据解析: ${job.inputData}`,
      `2025-08-20 ${new Date().toLocaleTimeString()} [数据解析] 数据解析成功, 共 1 行`,
    ];
    
    if (job.progress >= 15) {
      mockLogs.push(`2025-08-20 ${new Date().toLocaleTimeString()} [翻译] 开始翻译文本`);
      mockLogs.push(`2025-08-20 ${new Date().toLocaleTimeString()} [翻译] 开始翻译处理`);
    }
    
    if (job.status === 'failed') {
      mockLogs.push(`2025-08-20 ${new Date().toLocaleTimeString()} [错误] 处理失败: ${job.error}`);
    }
    
    setLogs(mockLogs);
  }, [job.progress, job.status]);

  return (
    <div className="space-y-6 max-w-4xl mx-auto p-6">
      {/* 标题区域 */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <div className="animate-spin text-4xl text-blue-500 mr-3">⚙️</div>
          <h2 className="text-3xl font-bold text-gray-800">正在生成内容，请稍候...</h2>
        </div>
        <p className="text-gray-600">AI正在处理您的文件，这可能需要几分钟时间</p>
      </div>

      {/* 当前步骤显示 */}
      <Card className="mb-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center">
            <span className="text-2xl mr-3">{currentStep.icon}</span>
            <div>
              <h3 className="text-lg font-semibold text-blue-600">
                正在{currentStep.title} ({currentStep.description})
              </h3>
              <p className="text-gray-500">步骤 {currentStepIndex + 1} / {PROCESSING_STEPS.length}</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-blue-600">{job.progress}%</div>
            <div className="text-sm text-gray-500">正在生成图片...</div>
          </div>
        </div>
        
        <Progress 
          percent={job.progress} 
          status={job.status === 'failed' ? 'exception' : 'active'}
          strokeWidth={8}
          className="mb-4"
        />
      </Card>

      {/* 任务进度提示 */}
      <Card title="📋 任务进度提示" className="mb-6">
        <Steps current={currentStepIndex} status={job.status === 'failed' ? 'error' : 'process'}>
          {PROCESSING_STEPS.map((step, index) => (
            <Step
              key={step.key}
              title={step.title}
              description={step.description}
              icon={getStepIcon(index)}
              status={getStepStatus(index)}
            />
          ))}
        </Steps>
      </Card>

      {/* 提示信息 */}
      <Card className="bg-yellow-50 border-yellow-200">
        <div className="flex items-center">
          <span className="text-2xl mr-3">⚠️</span>
          <div>
            <p className="text-yellow-800 font-medium">生成过程中请勿关闭页面，完成后会自动跳转到结果页面。</p>
            <p className="text-yellow-700 text-sm mt-1">系统会自动检查网络波动并尝试恢复处理，系统会自动检查网络检索并尝试恢复处理。</p>
          </div>
        </div>
      </Card>

      {/* 操作按钮 */}
      <div className="flex justify-center space-x-4">
        <Button 
          size="large" 
          onClick={() => setShowLogModal(true)}
          className="px-6"
          icon={<EyeOutlined />}
        >
          查看处理日志
        </Button>
        
        <Button 
          type="primary" 
          danger 
          size="large"
          className="px-6"
          onClick={() => {
            // 取消任务逻辑 - 返回首页
            onBack();
          }}
        >
          取消任务
        </Button>
      </div>

      {/* 错误显示 */}
      {job.status === 'failed' && job.error && (
        <Card className="border-red-300 bg-red-50">
          <div className="flex items-center">
            <CloseOutlined className="text-red-500 text-xl mr-3" />
            <div>
              <h3 className="text-red-800 font-semibold">处理失败</h3>
              <p className="text-red-700">{job.error}</p>
              <div className="mt-3">
                <Button type="primary" danger onClick={onRetry} className="mr-2">
                  重试
                </Button>
                <Button onClick={() => setShowLogModal(true)} icon={<EyeOutlined />}>
                  查看详细日志
                </Button>
              </div>
            </div>
          </div>
        </Card>
      )}

      {/* 日志查看模态框 */}
      <Modal
        title={`任务生成日志 - ${job.id}`}
        open={showLogModal}
        onCancel={() => setShowLogModal(false)}
        footer={null}
        width={800}
        className="log-modal"
      >
        <div 
          className="log-container"
          style={{
            backgroundColor: '#1e1e1e',
            color: '#d4d4d4',
            padding: '16px',
            borderRadius: '6px',
            fontFamily: 'Consolas, "Courier New", monospace',
            fontSize: '14px',
            lineHeight: '1.4',
            maxHeight: '400px',
            overflowY: 'auto'
          }}
        >
          {logs.map((log, index) => (
            <div key={index} style={{ marginBottom: '4px' }}>
              {log.includes('[初始化]') && <span style={{ color: '#4FC3F7' }}>{log}</span>}
              {log.includes('[数据解析]') && <span style={{ color: '#66BB6A' }}>{log}</span>}
              {log.includes('[翻译]') && <span style={{ color: '#FFA726' }}>{log}</span>}
              {log.includes('[错误]') && <span style={{ color: '#EF5350' }}>{log}</span>}
              {!log.includes('[') && <span style={{ color: '#d4d4d4' }}>{log}</span>}
            </div>
          ))}
        </div>
      </Modal>
    </div>
  );
};

export default ProcessingProgress;