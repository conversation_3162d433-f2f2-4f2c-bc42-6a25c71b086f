# 内容生成器 - 完整设置指南

## 🎯 项目概述

这是一个基于AI的智能内容生成器，可以将英文文本或音频转换为包含配音、字幕和图像的视频内容。

### 主要功能
- **音频转录**: 使用 OpenAI Whisper 将音频转换为文本
- **文本简化**: 根据词汇水平简化英文文本
- **多语言翻译**: 英文到中文的智能翻译
- **图像生成**: 使用 DALL-E 生成相关图片
- **语音合成**: 使用 OpenAI TTS 生成高质量语音
- **字幕生成**: 自动生成 SRT 格式字幕
- **视频合成**: 使用 FFmpeg 合成最终视频

## 🚀 快速开始

### 1. 环境要求
- Node.js 18+
- OpenAI API Key
- FFmpeg (Docker版本已包含)

### 2. 安装步骤

#### Windows用户
```bash
# 1. 克隆或下载项目
cd content-generator

# 2. 运行设置脚本
scripts\setup.bat

# 3. 编辑 .env 文件，添加你的 OpenAI API Key
# OPENAI_API_KEY=your_api_key_here

# 4. 启动开发服务器
npm run dev
```

#### Linux/Mac用户
```bash
# 1. 克隆或下载项目
cd content-generator

# 2. 运行设置脚本
chmod +x scripts/setup.sh
./scripts/setup.sh

# 3. 编辑 .env 文件，添加你的 OpenAI API Key
# OPENAI_API_KEY=your_api_key_here

# 4. 启动开发服务器
npm run dev
```

### 3. 访问应用
- 前端界面: http://localhost:5173
- 后端API: http://localhost:3001
- API文档: http://localhost:3001/api/health

## 🔧 开发模式

### 启动开发服务器
```bash
# 同时启动前后端
npm run dev

# 或分别启动
npm run dev:frontend  # 前端: http://localhost:5173
npm run dev:backend   # 后端: http://localhost:3001
```

### 测试后端功能
```bash
cd backend
npm run test:basic  # 运行基础测试
npm test           # 运行完整测试套件
```

## 🐳 Docker部署

### 开发环境
```bash
# 启动后端服务（前端在本地运行）
npm run docker:dev

# 前端仍需本地启动
cd frontend && npm run dev
```

### 生产环境
```bash
# 构建并启动所有服务
npm run docker:prod

# 访问: http://localhost
```

## 📁 项目结构

```
content-generator/
├── backend/                 # 后端服务
│   ├── src/
│   │   ├── routes/         # API路由
│   │   ├── services/       # 业务逻辑
│   │   │   ├── openai.ts   # OpenAI服务集成
│   │   │   ├── subtitle.ts # 字幕生成
│   │   │   ├── video.ts    # 视频合成
│   │   │   └── ...
│   │   └── index.ts        # 服务器入口
│   ├── uploads/            # 上传文件目录
│   ├── outputs/            # 输出文件目录
│   └── package.json
├── frontend/               # 前端应用
│   ├── src/
│   │   ├── components/     # React组件
│   │   ├── services/       # API服务
│   │   └── types/          # 类型定义
│   └── package.json
├── shared/                 # 共享类型定义
├── scripts/               # 设置脚本
├── docker-compose.yml     # Docker配置
└── README.md
```

## 🎮 使用方法

### 1. 准备输入内容
- **文件上传**: 支持 MP3、WAV、M4A 音频文件或 TXT 文本文件
- **URL输入**: 直接输入音频文件的网络链接

### 2. 配置生成参数
- **词汇等级**: 500-5000词，适应不同学习水平
- **翻译选项**: 是否生成中文翻译
- **图像生成**: 选择是否生成配图及风格（扁平/写实/卡通）
- **语音设置**: 选择发音人和语速
- **输出格式**: 选择视频分辨率

### 3. 开始生成
系统将自动执行以下步骤：
1. 音频转录（如果是音频输入）
2. 文本简化
3. 中文翻译（如果启用）
4. 生成语音
5. 生成图像（如果启用）
6. 创建字幕
7. 合成视频

### 4. 下载结果
- 最终视频文件 (MP4)
- 音频文件 (MP3)
- 字幕文件 (SRT)
- 生成的图片
- 处理后的文本

## 🔍 故障排除

### 常见问题

1. **OpenAI API 错误**
   - 检查 API Key 是否正确
   - 确认账户余额充足
   - 检查网络连接

2. **文件上传失败**
   - 检查文件大小（最大50MB）
   - 确认文件格式支持
   - 检查磁盘空间

3. **视频生成失败**
   - 确认 FFmpeg 已安装（Docker版本已包含）
   - 检查输出目录权限
   - 查看后端日志

4. **WebSocket 连接失败**
   - 检查防火墙设置
   - 确认端口未被占用
   - 刷新页面重试

### 查看日志
```bash
# 开发模式下，日志会在控制台显示

# Docker模式下
docker-compose logs -f backend
docker-compose logs -f frontend
```

## 🔒 安全注意事项

- 确保 OpenAI API Key 安全存储
- 定期清理上传的敏感文件
- 生产环境建议添加用户认证
- 设置适当的文件大小和请求频率限制

## 📞 技术支持

如果遇到问题，请检查：
1. Node.js 版本是否为 18+
2. OpenAI API Key 是否正确设置
3. 网络连接是否正常
4. 磁盘空间是否充足

## 🎉 开始使用

现在你可以开始使用内容生成器了！访问 http://localhost:5173 开始创建你的第一个AI生成视频。
