export interface GenerationParams {
  vocabularyLevel: number;
  translateToChinese: boolean;
  generateImages: boolean;
  imageStyle: 'flat' | 'realistic' | 'cartoon';
  speechSpeed: 'slow' | 'normal' | 'fast';
  voiceType: string;
  outputResolution: string;
}

export interface ProcessingJob {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  inputType: 'file' | 'url';
  inputData: string;
  params: GenerationParams;
  progress: number;
  result?: {
    videoUrl?: string;
    subtitleUrl?: string;
    audioUrl?: string;
    images?: string[];
    transcription?: string;
    simplifiedText?: string;
    translation?: string;
    duration?: number;
  };
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UploadedFile {
  id: string;
  originalName: string;
  filename: string;
  path: string;
  size: number;
  mimetype: string;
  uploadedAt: string;
}

export interface ProcessingStep {
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  result?: any;
  error?: string;
}

export interface JobStats {
  total: number;
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  activeJobs: number;
  maxConcurrentJobs: number;
  queueLength: number;
}