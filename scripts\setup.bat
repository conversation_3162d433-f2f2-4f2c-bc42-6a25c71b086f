@echo off
setlocal enabledelayedexpansion

echo 🚀 Setting up Content Generator...

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

echo ✅ Node.js detected

REM Check if Docker is installed (optional)
docker --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️  Docker not found. Docker deployment will not be available.
    set DOCKER_AVAILABLE=false
) else (
    echo ✅ Docker detected
    set DOCKER_AVAILABLE=true
)

REM Create environment file if it doesn't exist
if not exist .env (
    echo 📝 Creating .env file...
    copy backend\.env.example .env
    echo ⚠️  Please edit .env file and add your OpenAI API key!
) else (
    echo ✅ .env file already exists
)

REM Install dependencies
echo 📦 Installing dependencies...
call npm run install:all

REM Create necessary directories
echo 📁 Creating directories...
if not exist backend\uploads mkdir backend\uploads
if not exist backend\outputs mkdir backend\outputs
if not exist logs mkdir logs

echo ✅ Setup completed!
echo.
echo Next steps:
echo 1. Edit .env file and add your OpenAI API key
echo 2. Run 'npm run dev' to start development servers
if "%DOCKER_AVAILABLE%"=="true" (
    echo 3. Or run 'npm run docker:dev' to start with Docker
)
echo.
echo For production deployment:
if "%DOCKER_AVAILABLE%"=="true" (
    echo - Run 'npm run docker:prod' to start with Docker
)
echo - Or run 'npm run build && npm start' for local production

pause
