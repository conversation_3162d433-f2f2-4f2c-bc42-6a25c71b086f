import cron from 'node-cron';
import fs from 'fs';
import path from 'path';

class CleanupService {
  private isRunning = false;

  public start() {
    if (this.isRunning) {
      console.log('Cleanup service is already running');
      return;
    }

    console.log('Starting cleanup service...');
    this.isRunning = true;

    // Run cleanup every day at 2 AM
    cron.schedule('0 2 * * *', () => {
      console.log('Running scheduled cleanup...');
      this.cleanupTempFiles();
      this.cleanupOldOutputs();
    });

    // Also run cleanup on startup
    setTimeout(() => {
      this.cleanupTempFiles();
      this.cleanupOldOutputs();
    }, 5000);
  }

  public stop() {
    this.isRunning = false;
    console.log('Cleanup service stopped');
  }

  private cleanupTempFiles() {
    try {
      const uploadDir = process.env.UPLOAD_DIR || './uploads';
      const maxAge = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
      
      if (!fs.existsSync(uploadDir)) {
        return;
      }

      const files = fs.readdirSync(uploadDir);
      let deletedCount = 0;

      files.forEach(filename => {
        const filePath = path.join(uploadDir, filename);
        
        try {
          const stats = fs.statSync(filePath);
          const age = Date.now() - stats.mtime.getTime();
          
          if (age > maxAge) {
            fs.unlinkSync(filePath);
            deletedCount++;
            console.log(`Deleted old upload file: ${filename}`);
          }
        } catch (error) {
          console.error(`Error processing file ${filename}:`, error);
        }
      });

      if (deletedCount > 0) {
        console.log(`Cleaned up ${deletedCount} old upload files`);
      }

    } catch (error) {
      console.error('Error during temp files cleanup:', error);
    }
  }

  private cleanupOldOutputs() {
    try {
      const outputDir = process.env.OUTPUT_DIR || './outputs';
      const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds
      
      if (!fs.existsSync(outputDir)) {
        return;
      }

      const files = fs.readdirSync(outputDir);
      let deletedCount = 0;

      files.forEach(filename => {
        const filePath = path.join(outputDir, filename);
        
        try {
          const stats = fs.statSync(filePath);
          const age = Date.now() - stats.mtime.getTime();
          
          if (age > maxAge) {
            fs.unlinkSync(filePath);
            deletedCount++;
            console.log(`Deleted old output file: ${filename}`);
          }
        } catch (error) {
          console.error(`Error processing output file ${filename}:`, error);
        }
      });

      if (deletedCount > 0) {
        console.log(`Cleaned up ${deletedCount} old output files`);
      }

    } catch (error) {
      console.error('Error during output files cleanup:', error);
    }
  }

  public manualCleanup() {
    console.log('Running manual cleanup...');
    this.cleanupTempFiles();
    this.cleanupOldOutputs();
  }
}

export const cleanupService = new CleanupService();
