import React, { useState, useEffect, useRef } from 'react';
import { Card, message } from 'antd';
import InputSection from './InputSection';
import ParameterSection from './ParameterSection';
import ProcessingProgress from './ProcessingProgress';
import ResultsSection from './ResultsSection';
import { GenerationParams, ProcessingJob } from '../types';
import { processAPI, wsManager } from '../services/api';

const ContentGenerator: React.FC = () => {
  const [activeStep, setActiveStep] = useState<'input' | 'processing' | 'results'>('input');
  const [currentJob, setCurrentJob] = useState<ProcessingJob | null>(null);
  const [wsConnected, setWsConnected] = useState(false);
  const currentJobIdRef = useRef<string | null>(null);
  
  console.log('ContentGenerator render - activeStep:', activeStep, 'currentJob:', currentJob);

  // 默认参数
  const [params, setParams] = useState<GenerationParams>({
    vocabularyLevel: 1500,
    translateToChinese: true,
    generateImages: false,
    imageStyle: 'flat',
    speechSpeed: 'normal',
    voiceType: 'harry',
    outputResolution: '1024x1024'
  });

  useEffect(() => {
    // 初始化 WebSocket 连接（仅一次），并在重连后自动订阅当前任务
    wsManager.connect();

    wsManager.on('connected', () => {
      setWsConnected(true);
      if (currentJobIdRef.current) {
        wsManager.subscribeToJob(currentJobIdRef.current);
      }
    });

    wsManager.on('disconnected', () => {
      setWsConnected(false);
    });

    wsManager.on('job_update', (data) => {
      if (currentJobIdRef.current && data.jobId === currentJobIdRef.current) {
        setCurrentJob(prev => prev ? { ...prev, ...data.data } : null);
        if (data.data.status === 'completed') {
          setActiveStep('results');
          message.success('内容生成完成！');
        } else if (data.data.status === 'failed') {
          setActiveStep('processing');
          message.error(`任务失败: ${data.data.error}`);
        }
      }
    });

    wsManager.on('step_update', (data) => {
      if (currentJobIdRef.current && data.jobId === currentJobIdRef.current) {
        console.log('Step update:', data);
      }
    });

    return () => {
      wsManager.disconnect();
    };
  }, []);

  // 当 WS 断开时，定时轮询作为兜底，确保进度不会卡在 5%
  useEffect(() => {
    let intervalId: number | undefined;
    if (!wsConnected && currentJobIdRef.current) {
      intervalId = window.setInterval(async () => {
        try {
          const job = await processAPI.getStatus(currentJobIdRef.current as string);
          setCurrentJob(job);
          if (job.status === 'completed') {
            setActiveStep('results');
            window.clearInterval(intervalId);
          } else if (job.status === 'failed') {
            setActiveStep('processing');
            window.clearInterval(intervalId);
          }
        } catch (e) {
          // 忽略网络错误，继续下一次轮询
        }
      }, 2000);
    }
    return () => {
      if (intervalId) window.clearInterval(intervalId);
    };
  }, [wsConnected]);

  const handleStartProcessing = async (inputType: 'file' | 'url', inputData: string) => {
    try {
      console.log('Starting processing with:', { inputType, inputData });
      const response = await processAPI.startProcess(inputType, inputData, params);
      console.log('Process API response:', response);
      
      // 获取完整的任务信息
      const job = await processAPI.getStatus(response.jobId);
      console.log('Job status retrieved:', job);
      setCurrentJob(job);
      currentJobIdRef.current = job.id;
      setActiveStep('processing');
      console.log('Active step set to processing');
      
      // 订阅 WebSocket 更新
      wsManager.subscribeToJob(response.jobId);
      
      message.success('任务已开始处理');
    } catch (error: any) {
      console.error('Start processing error:', error);
      message.error(`启动处理失败: ${error.response?.data?.error || error.message}`);
    }
  };

  const handleBackToInput = () => {
    setActiveStep('input');
    setCurrentJob(null);
  };


  return (
    <div className="space-y-6">
      {/* 连接状态指示 */}
      <div className="text-center">
        <div className={`inline-flex items-center space-x-2 px-3 py-1 rounded-full text-sm ${
          wsConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
        }`}>
          <div className={`w-2 h-2 rounded-full ${wsConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
          <span>{wsConnected ? '实时连接已建立' : '连接断开'}</span>
        </div>
      </div>

      {/* 主内容区域 */}
      <Card className="main-card" bordered={false}>
        {activeStep === 'input' && (
          <>
            <InputSection
              onStartProcessing={handleStartProcessing}
              disabled={!wsConnected}
            />
            <div className="mt-8">
              <ParameterSection
                params={params}
                onParamsChange={setParams}
              />
            </div>
          </>
        )}

        {activeStep === 'processing' && currentJob && (
          <ProcessingProgress
            job={currentJob}
            onBack={handleBackToInput}
            onRetry={() => handleStartProcessing(currentJob.inputType, currentJob.inputData)}
          />
        )}

        {activeStep === 'results' && currentJob && (
          <ResultsSection
            job={currentJob}
            onBack={handleBackToInput}
            onRestart={() => handleStartProcessing(currentJob.inputType, currentJob.inputData)}
          />
        )}
      </Card>

    </div>
  );
};

export default ContentGenerator;