{"name": "content-generator", "version": "1.0.0", "description": "AI-powered content generator with audio, text and video processing", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:frontend": "cd frontend && npm run preview", "start:backend": "cd backend && npm start", "install:all": "npm install && cd frontend && npm install && cd ../backend && npm install", "docker:dev": "docker-compose -f docker-compose.dev.yml up --build", "docker:prod": "docker-compose up --build -d", "docker:down": "docker-compose down", "test": "cd backend && npm test", "lint": "cd frontend && npm run lint"}, "devDependencies": {"concurrently": "^8.2.2"}}