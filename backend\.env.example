# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here

# Server Configuration
PORT=3001
NODE_ENV=development

# File Storage
UPLOAD_DIR=./uploads
OUTPUT_DIR=./outputs
MAX_FILE_SIZE=52428800

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# Processing Configuration
MAX_CONCURRENT_JOBS=3
JOB_TIMEOUT=1800000

# Database (Optional - for future use)
# DATABASE_URL=sqlite:./data/jobs.db

# Logging
LOG_LEVEL=info
