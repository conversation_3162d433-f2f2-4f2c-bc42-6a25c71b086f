import OpenAI from 'openai';
import fs from 'fs';
import path from 'path';
import axios from 'axios';
import { v4 as uuidv4 } from 'uuid';
import { 
  AudioTranscription, 
  TranscriptionSegment, 
  SimplifiedText, 
  Translation, 
  ExtractedKeywords, 
  GeneratedImage, 
  GeneratedAudio 
} from '../../../shared/types';

export class OpenAIService {
  private client: OpenAI;
  private outputDir: string;

  constructor() {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY environment variable is required');
    }

    this.client = new OpenAI({ apiKey });
    this.outputDir = process.env.OUTPUT_DIR || './outputs';
  }

  // Transcribe audio using Whisper
  async transcribeAudio(audioPath: string): Promise<AudioTranscription> {
    try {
      console.log(`Transcribing audio: ${audioPath}`);

      const audioFile = fs.createReadStream(audioPath);
      
      const response = await this.client.audio.transcriptions.create({
        file: audioFile,
        model: 'whisper-1',
        response_format: 'verbose_json',
        timestamp_granularities: ['segment']
      });

      // Convert OpenAI response to our format
      const segments: TranscriptionSegment[] = response.segments?.map(segment => ({
        start: segment.start,
        end: segment.end,
        text: segment.text.trim()
      })) || [];

      const transcription: AudioTranscription = {
        text: response.text,
        segments
      };

      console.log(`Transcription completed: ${transcription.text.length} characters`);
      return transcription;

    } catch (error) {
      console.error('Transcription error:', error);
      throw new Error(`Failed to transcribe audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Simplify text based on vocabulary level
  async simplifyText(text: string, vocabularyLevel: number): Promise<SimplifiedText> {
    try {
      console.log(`Simplifying text for vocabulary level: ${vocabularyLevel}`);

      const prompt = `Please simplify the following English text to match a vocabulary level of approximately ${vocabularyLevel} most common English words. 

Guidelines:
- Keep the meaning and main ideas intact
- Replace complex words with simpler alternatives
- Shorten long sentences if needed
- Maintain natural flow and readability
- If the text is already simple enough, you may keep it mostly unchanged

Original text:
${text}

Simplified text:`;

      const response = await this.client.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at simplifying English text for language learners. Always respond with just the simplified text, no explanations or additional comments.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 2000
      });

      const simplifiedText = response.choices[0]?.message?.content?.trim() || text;

      console.log(`Text simplification completed: ${text.length} -> ${simplifiedText.length} characters`);

      return {
        original: text,
        simplified: simplifiedText,
        vocabularyLevel
      };

    } catch (error) {
      console.error('Text simplification error:', error);
      throw new Error(`Failed to simplify text: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Translate text to Chinese
  async translateText(englishText: string): Promise<Translation> {
    try {
      console.log(`Translating text to Chinese: ${englishText.length} characters`);

      const prompt = `Please translate the following English text to Chinese. Provide a natural, accurate translation that maintains the original meaning and tone.

English text:
${englishText}

Chinese translation:`;

      const response = await this.client.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert translator specializing in English to Chinese translation. Always respond with just the Chinese translation, no explanations or additional comments.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.2,
        max_tokens: 2000
      });

      const chineseText = response.choices[0]?.message?.content?.trim() || '';

      console.log(`Translation completed: ${chineseText.length} characters`);

      return {
        english: englishText,
        chinese: chineseText
      };

    } catch (error) {
      console.error('Translation error:', error);
      throw new Error(`Failed to translate text: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Extract keywords and generate image prompts
  async extractKeywords(text: string): Promise<ExtractedKeywords> {
    try {
      console.log(`Extracting keywords from text: ${text.length} characters`);

      const prompt = `Analyze the following text and extract:
1. Key concepts and important words (5-10 keywords)
2. Visual elements that could be illustrated (3-5 image prompts)

For image prompts, create descriptive phrases that would work well with DALL-E image generation. Focus on concrete, visual elements rather than abstract concepts.

Text:
${text}

Please respond in this exact JSON format:
{
  "keywords": ["keyword1", "keyword2", "keyword3"],
  "imagePrompts": ["prompt1", "prompt2", "prompt3"]
}`;

      const response = await this.client.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert at analyzing text and extracting visual elements. Always respond with valid JSON only.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 500
      });

      const responseText = response.choices[0]?.message?.content?.trim() || '';
      
      try {
        const parsed = JSON.parse(responseText);
        
        return {
          text,
          keywords: parsed.keywords || [],
          imagePrompts: parsed.imagePrompts || []
        };
      } catch (parseError) {
        console.warn('Failed to parse keywords JSON, using fallback');
        return {
          text,
          keywords: [],
          imagePrompts: []
        };
      }

    } catch (error) {
      console.error('Keyword extraction error:', error);
      throw new Error(`Failed to extract keywords: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Generate images using DALL-E
  async generateImages(prompts: string[], style: string = 'flat'): Promise<GeneratedImage[]> {
    try {
      console.log(`Generating ${prompts.length} images with style: ${style}`);

      const stylePrompts = {
        flat: 'flat design illustration, minimalist, clean lines, simple shapes',
        realistic: 'photorealistic, high quality, detailed',
        cartoon: 'cartoon style, colorful, friendly, animated'
      };

      const stylePrefix = stylePrompts[style as keyof typeof stylePrompts] || stylePrompts.flat;
      const images: GeneratedImage[] = [];

      for (let i = 0; i < prompts.length; i++) {
        const prompt = prompts[i];
        const enhancedPrompt = `${stylePrefix}, ${prompt}`;

        try {
          const response = await this.client.images.generate({
            model: 'dall-e-3',
            prompt: enhancedPrompt,
            size: '1024x1024',
            quality: 'standard',
            n: 1
          });

          const imageUrl = response.data?.[0]?.url;
          if (imageUrl) {
            // Download and save the image
            const filename = `image_${uuidv4()}.jpg`;
            const filepath = path.join(this.outputDir, filename);

            const imageResponse = await axios.get(imageUrl, { responseType: 'stream' });
            const writer = fs.createWriteStream(filepath);
            imageResponse.data.pipe(writer);

            await new Promise<void>((resolve, reject) => {
              writer.on('finish', () => resolve());
              writer.on('error', reject);
            });

            images.push({
              prompt: enhancedPrompt,
              url: `/outputs/${filename}`,
              segmentIndex: i
            });

            console.log(`Generated image ${i + 1}/${prompts.length}: ${filename}`);
          }
        } catch (imageError) {
          console.error(`Error generating image ${i + 1}:`, imageError);
          // Continue with other images
        }
      }

      console.log(`Image generation completed: ${images.length}/${prompts.length} successful`);
      return images;

    } catch (error) {
      console.error('Image generation error:', error);
      throw new Error(`Failed to generate images: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Generate speech using TTS
  async generateSpeech(text: string, voice: string = 'alloy', speed: number = 1.0): Promise<GeneratedAudio> {
    try {
      console.log(`Generating speech: ${text.length} characters, voice: ${voice}, speed: ${speed}`);

      // Map voice types to OpenAI voices
      const voiceMap: { [key: string]: string } = {
        'harry': 'alloy',
        'emma': 'nova',
        'brian': 'echo',
        'amy': 'shimmer',
        'arthur': 'onyx',
        'jenny': 'fable'
      };

      const openaiVoice = voiceMap[voice] || 'alloy';

      const response = await this.client.audio.speech.create({
        model: 'tts-1',
        voice: openaiVoice as any,
        input: text,
        speed: speed
      });

      // Save audio file
      const filename = `audio_${uuidv4()}.mp3`;
      const filepath = path.join(this.outputDir, filename);

      const buffer = Buffer.from(await response.arrayBuffer());
      fs.writeFileSync(filepath, buffer);

      // Estimate duration (rough calculation: ~150 words per minute)
      const wordCount = text.split(/\s+/).length;
      const estimatedDuration = (wordCount / 150) * 60 / speed; // in seconds

      console.log(`Speech generation completed: ${filename}, estimated duration: ${estimatedDuration.toFixed(1)}s`);

      return {
        text,
        audioUrl: `/outputs/${filename}`,
        duration: estimatedDuration
      };

    } catch (error) {
      console.error('Speech generation error:', error);
      throw new Error(`Failed to generate speech: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Download audio from URL
  async downloadAudioFromUrl(url: string): Promise<string> {
    try {
      console.log(`Downloading audio from URL: ${url}`);

      const response = await axios.get(url, {
        responseType: 'stream',
        timeout: 30000,
        headers: {
          'User-Agent': 'Content-Generator/1.0'
        }
      });

      const filename = `download_${uuidv4()}.${this.getFileExtensionFromUrl(url) || 'mp3'}`;
      const filepath = path.join(this.outputDir, filename);

      const writer = fs.createWriteStream(filepath);
      response.data.pipe(writer);

      await new Promise<void>((resolve, reject) => {
        writer.on('finish', () => resolve());
        writer.on('error', reject);
      });

      console.log(`Audio downloaded: ${filename}`);
      return filepath;

    } catch (error) {
      console.error('Audio download error:', error);
      throw new Error(`Failed to download audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private getFileExtensionFromUrl(url: string): string | null {
    try {
      const pathname = new URL(url).pathname;
      const extension = path.extname(pathname).slice(1).toLowerCase();

      const audioExtensions = ['mp3', 'wav', 'm4a', 'ogg', 'webm', 'flac'];
      return audioExtensions.includes(extension) ? extension : null;
    } catch {
      return null;
    }
  }
}
