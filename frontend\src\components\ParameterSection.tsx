import React from 'react';
import { Row, Col, Select } from 'antd';
import { SoundOutlined, TranslationOutlined, PictureOutlined, SettingOutlined } from '@ant-design/icons';
import type { GenerationParams } from '../types';

const { Option } = Select;

interface ParameterSectionProps {
  params: GenerationParams;
  onParamsChange: (params: GenerationParams) => void;
}

const ParameterSection: React.FC<ParameterSectionProps> = ({ params, onParamsChange }) => {
  const updateParam = <K extends keyof GenerationParams>(key: K, value: GenerationParams[K]) => {
    onParamsChange({ ...params, [key]: value });
  };

  return (
    <div className="parameter-card p-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-6 flex items-center">
        <SettingOutlined className="mr-2" />
        2. 设置生成参数
      </h3>

      <Row gutter={[24, 24]}>
        {/* 词汇等级 */}
        <Col xs={24} md={12}>
          <div className="space-y-3">
            <label className="flex items-center text-sm font-medium text-gray-700">
              <TranslationOutlined className="mr-2 text-blue-500" />
              词汇等级
            </label>
            <Select
              value={`${params.vocabularyLevel}词`}
              onChange={(value) => updateParam('vocabularyLevel', parseInt(value.replace('词', '')))}
              className="w-full"
            >
              <Option value="500词">500词（入门）</Option>
              <Option value="1000词">1000词（基础）</Option>
              <Option value="1500词">1500词（入门）</Option>
              <Option value="2000词">2000词（进阶）</Option>
              <Option value="3000词">3000词（中级）</Option>
              <Option value="5000词">5000词（高级）</Option>
            </Select>
          </div>
        </Col>

        {/* 是否翻译中文 */}
        <Col xs={24} md={12}>
          <div className="space-y-3">
            <label className="flex items-center text-sm font-medium text-gray-700">
              <TranslationOutlined className="mr-2 text-green-500" />
              是否翻译中文
            </label>
            <Select
              value={params.translateToChinese ? '是' : '否'}
              onChange={(value) => updateParam('translateToChinese', value === '是')}
              className="w-full"
            >
              <Option value="是">是</Option>
              <Option value="否">否</Option>
            </Select>
          </div>
        </Col>

        {/* 是否生成图片 */}
        <Col xs={24} md={12}>
          <div className="space-y-3">
            <label className="flex items-center text-sm font-medium text-gray-700">
              <PictureOutlined className="mr-2 text-purple-500" />
              是否生成图片
            </label>
            <Select
              value={params.generateImages ? '生成图片' : '不生成图片'}
              onChange={(value) => updateParam('generateImages', value === '生成图片')}
              className="w-full"
            >
              <Option value="不生成图片">不生成图片</Option>
              <Option value="生成图片">生成图片</Option>
            </Select>
          </div>
        </Col>

        {/* 图像风格 */}
        <Col xs={24} md={12}>
          <div className="space-y-3">
            <label className="flex items-center text-sm font-medium text-gray-700">
              <PictureOutlined className="mr-2 text-orange-500" />
              图像风格
            </label>
            <Select
              value={`${params.imageStyle} (${getStyleLabel(params.imageStyle)})`}
              onChange={(value) => {
                const style = value.split(' ')[0] as 'flat' | 'realistic' | 'cartoon';
                updateParam('imageStyle', style);
              }}
              className="w-full"
              disabled={!params.generateImages}
            >
              <Option value="flat (扁平插画)">flat（扁平插画）</Option>
              <Option value="realistic (写实风格)">realistic（写实风格）</Option>
              <Option value="cartoon (卡通风格)">cartoon（卡通风格）</Option>
            </Select>
          </div>
        </Col>

        {/* 配音语速 */}
        <Col xs={24} md={12}>
          <div className="space-y-3">
            <label className="flex items-center text-sm font-medium text-gray-700">
              <SoundOutlined className="mr-2 text-red-500" />
              配音语速
            </label>
            <Select
              value={params.speechSpeed}
              onChange={(value) => updateParam('speechSpeed', value)}
              className="w-full"
            >
              <Option value="slow">slow（慢速）</Option>
              <Option value="normal">normal（正常）</Option>
              <Option value="fast">fast（快速）</Option>
            </Select>
          </div>
        </Col>

        {/* 配音发音人 */}
        <Col xs={24} md={12}>
          <div className="space-y-3">
            <label className="flex items-center text-sm font-medium text-gray-700">
              <SoundOutlined className="mr-2 text-blue-500" />
              配音发音人
            </label>
            <Select
              value={`${params.voiceType} - ${getVoiceLabel(params.voiceType)}`}
              onChange={(value) => {
                const voiceType = value.split(' - ')[0];
                updateParam('voiceType', voiceType);
              }}
              className="w-full"
            >
              <Option value="harry - 英音男声">harry - 英音男声</Option>
              <Option value="emma - 英音女声">emma - 英音女声</Option>
              <Option value="john - 美音男声">john - 美音男声</Option>
              <Option value="sarah - 美音女声">sarah - 美音女声</Option>
            </Select>
          </div>
        </Col>

        {/* 输出分辨率 */}
        <Col xs={24}>
          <div className="space-y-3">
            <label className="flex items-center text-sm font-medium text-gray-700">
              <SettingOutlined className="mr-2 text-gray-500" />
              输出分辨率
            </label>
            <Select
              value={`${params.outputResolution} (${getResolutionLabel(params.outputResolution)})`}
              onChange={(value) => {
                const resolution = value.split(' ')[0];
                updateParam('outputResolution', resolution);
              }}
              className="w-full"
            >
              <Option value="1024x1024 (方形)">1024×1024（方形）</Option>
              <Option value="1280x720 (横屏)">1280×720（横屏）</Option>
              <Option value="720x1280 (竖屏)">720×1280（竖屏）</Option>
              <Option value="1920x1080 (全高清)">1920×1080（全高清）</Option>
            </Select>
          </div>
        </Col>
      </Row>

      {/* 参数预览 */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="text-sm font-medium text-gray-700 mb-2">当前设置预览：</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs text-gray-600">
          <div>词汇：{params.vocabularyLevel}词</div>
          <div>翻译：{params.translateToChinese ? '是' : '否'}</div>
          <div>图片：{params.generateImages ? '生成' : '不生成'}</div>
          <div>语速：{params.speechSpeed}</div>
        </div>
      </div>
    </div>
  );
};

const getStyleLabel = (style: string): string => {
  const labels: Record<string, string> = {
    flat: '扁平插画',
    realistic: '写实风格', 
    cartoon: '卡通风格'
  };
  return labels[style] || style;
};

const getVoiceLabel = (voice: string): string => {
  const labels: Record<string, string> = {
    harry: '英音男声',
    emma: '英音女声',
    john: '美音男声',
    sarah: '美音女声'
  };
  return labels[voice] || voice;
};

const getResolutionLabel = (resolution: string): string => {
  const labels: Record<string, string> = {
    '1024x1024': '方形',
    '1280x720': '横屏',
    '720x1280': '竖屏',
    '1920x1080': '全高清'
  };
  return labels[resolution] || resolution;
};

export default ParameterSection;