// import React from 'react';
import { Layout } from 'antd';
import ContentGenerator from './components/ContentGenerator';

const { Header, Content } = Layout;

function App() {
  return (
    <Layout className="min-h-screen">
      <Header className="bg-white shadow-sm border-b border-gray-200">
        <div className="flex items-center justify-center h-full">
          <h1 className="text-xl font-semibold text-gray-800 m-0">
            内容生成器 v1.0
          </h1>
        </div>
      </Header>
      
      <Content className="content-generator-container p-6">
        <div className="max-w-4xl mx-auto">
          <ContentGenerator />
        </div>
      </Content>
    </Layout>
  );
}

export default App;