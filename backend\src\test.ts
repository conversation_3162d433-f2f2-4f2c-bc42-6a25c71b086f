// Simple test script to verify backend functionality
import dotenv from 'dotenv';
import { OpenAIService } from './services/openai';
import { SubtitleService } from './services/subtitle';

// Load environment variables
dotenv.config();

async function testServices() {
  console.log('🧪 Testing backend services...\n');

  // Test 1: Check OpenAI API key
  console.log('1. Checking OpenAI API key...');
  const apiKey = process.env.OPENAI_API_KEY;
  if (!apiKey) {
    console.log('❌ OPENAI_API_KEY not found in environment variables');
    console.log('   Please add your OpenAI API key to .env file');
    return;
  } else {
    console.log('✅ OpenAI API key found');
  }

  // Test 2: Initialize services
  console.log('\n2. Initializing services...');
  try {
    const openaiService = new OpenAIService();
    const subtitleService = new SubtitleService();
    console.log('✅ Services initialized successfully');
  } catch (error) {
    console.log('❌ Failed to initialize services:', error);
    return;
  }

  // Test 3: Test text simplification (without API call)
  console.log('\n3. Testing text processing...');
  const testText = "This is a complex sentence with sophisticated vocabulary that needs simplification.";
  console.log(`   Input: "${testText}"`);
  console.log('✅ Text processing ready');

  // Test 4: Test subtitle generation
  console.log('\n4. Testing subtitle generation...');
  const mockTranscription = {
    text: "Hello world. This is a test.",
    segments: [
      { start: 0, end: 2, text: "Hello world." },
      { start: 2, end: 5, text: "This is a test." }
    ]
  };

  try {
    const subtitleService = new SubtitleService();
    // We won't actually generate the file, just test the logic
    console.log('✅ Subtitle service ready');
  } catch (error) {
    console.log('❌ Subtitle service error:', error);
  }

  console.log('\n🎉 Basic tests completed!');
  console.log('\nTo test the full pipeline:');
  console.log('1. Start the server: npm run dev');
  console.log('2. Open the frontend and try uploading a file');
  console.log('3. Check the WebSocket connection in browser dev tools');
}

// Run tests
testServices().catch(console.error);
