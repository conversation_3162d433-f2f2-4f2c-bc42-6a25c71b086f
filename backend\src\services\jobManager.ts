import { v4 as uuidv4 } from 'uuid';
import { ProcessingJob, GenerationParams, ProcessingStep } from '../../../shared/types';
import { WebSocketManager } from './websocket';
import { ProcessingPipeline } from './processingPipeline';

export class JobManager {
  private jobs: Map<string, ProcessingJob> = new Map();
  private activeJobs: Map<string, ProcessingPipeline> = new Map();
  private maxConcurrentJobs: number;

  constructor(private wsManager: WebSocketManager) {
    this.maxConcurrentJobs = parseInt(process.env.MAX_CONCURRENT_JOBS || '3');
  }

  public async createJob(
    inputType: 'file' | 'url',
    inputData: string,
    params: GenerationParams
  ): Promise<ProcessingJob> {
    const jobId = uuidv4();
    const now = new Date();

    const job: ProcessingJob = {
      id: jobId,
      status: 'pending',
      inputType,
      inputData,
      params,
      progress: 0,
      createdAt: now,
      updatedAt: now
    };

    this.jobs.set(jobId, job);
    console.log(`Created job ${jobId} with input: ${inputType}:${inputData}`);

    // Start processing if we have capacity
    if (this.activeJobs.size < this.maxConcurrentJobs) {
      this.startProcessing(jobId);
    }

    return job;
  }

  public getJob(jobId: string): ProcessingJob | undefined {
    return this.jobs.get(jobId);
  }

  public getAllJobs(): ProcessingJob[] {
    return Array.from(this.jobs.values()).sort(
      (a, b) => b.createdAt.getTime() - a.createdAt.getTime()
    );
  }

  public getActiveJobsCount(): number {
    return this.activeJobs.size;
  }

  public async cancelJob(jobId: string): Promise<boolean> {
    const job = this.jobs.get(jobId);
    if (!job) return false;

    const pipeline = this.activeJobs.get(jobId);
    if (pipeline) {
      await pipeline.cancel();
      this.activeJobs.delete(jobId);
    }

    this.updateJob(jobId, {
      status: 'failed',
      error: 'Job cancelled by user',
      updatedAt: new Date()
    });

    return true;
  }

  public async restartJob(jobId: string): Promise<string> {
    const existingJob = this.jobs.get(jobId);
    if (!existingJob) {
      throw new Error('Job not found');
    }

    // Cancel existing job if it's running
    await this.cancelJob(jobId);

    // Create new job with same parameters
    const newJob = await this.createJob(
      existingJob.inputType,
      existingJob.inputData,
      existingJob.params
    );

    return newJob.id;
  }

  public deleteJob(jobId: string): boolean {
    const job = this.jobs.get(jobId);
    if (!job) return false;

    // Cancel if running
    this.cancelJob(jobId);

    // Remove from storage
    this.jobs.delete(jobId);
    
    console.log(`Deleted job ${jobId}`);
    return true;
  }

  public cleanupOldJobs(olderThanHours: number = 24): number {
    const cutoffTime = new Date(Date.now() - olderThanHours * 60 * 60 * 1000);
    let deletedCount = 0;

    this.jobs.forEach((job, jobId) => {
      if (job.createdAt < cutoffTime && job.status !== 'processing') {
        this.deleteJob(jobId);
        deletedCount++;
      }
    });

    console.log(`Cleaned up ${deletedCount} old jobs`);
    return deletedCount;
  }

  private async startProcessing(jobId: string) {
    const job = this.jobs.get(jobId);
    if (!job) return;

    console.log(`Starting processing for job ${jobId}`);

    // Update job status
    this.updateJob(jobId, {
      status: 'processing',
      progress: 0,
      updatedAt: new Date()
    });

    // Create processing pipeline
    const pipeline = new ProcessingPipeline(job, {
      onProgress: (progress: number) => {
        this.updateJob(jobId, {
          progress,
          updatedAt: new Date()
        });
      },
      onStepUpdate: (stepName: string, stepData: any) => {
        this.wsManager.broadcastStepUpdate(jobId, stepName, stepData);
      },
      onComplete: (result: any) => {
        this.updateJob(jobId, {
          status: 'completed',
          progress: 100,
          result,
          updatedAt: new Date()
        });
        this.activeJobs.delete(jobId);
        this.processNextJob();
      },
      onError: (error: string) => {
        this.updateJob(jobId, {
          status: 'failed',
          error,
          updatedAt: new Date()
        });
        this.activeJobs.delete(jobId);
        this.processNextJob();
      }
    });

    this.activeJobs.set(jobId, pipeline);

    try {
      await pipeline.start();
    } catch (error) {
      console.error(`Error starting pipeline for job ${jobId}:`, error);
      this.updateJob(jobId, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        updatedAt: new Date()
      });
      this.activeJobs.delete(jobId);
      this.processNextJob();
    }
  }

  private processNextJob() {
    if (this.activeJobs.size >= this.maxConcurrentJobs) {
      return;
    }

    // Find next pending job
    const pendingJob = Array.from(this.jobs.values())
      .filter(job => job.status === 'pending')
      .sort((a, b) => a.createdAt.getTime() - b.createdAt.getTime())[0];

    if (pendingJob) {
      this.startProcessing(pendingJob.id);
    }
  }

  private updateJob(jobId: string, updates: Partial<ProcessingJob>) {
    const job = this.jobs.get(jobId);
    if (!job) return;

    // Apply updates
    Object.assign(job, updates);

    // Broadcast update via WebSocket
    this.wsManager.broadcastJobUpdate(jobId, job);

    console.log(`Updated job ${jobId}:`, {
      status: job.status,
      progress: job.progress,
      error: job.error
    });
  }

  public getJobStats() {
    const jobs = Array.from(this.jobs.values());
    
    return {
      total: jobs.length,
      pending: jobs.filter(j => j.status === 'pending').length,
      processing: jobs.filter(j => j.status === 'processing').length,
      completed: jobs.filter(j => j.status === 'completed').length,
      failed: jobs.filter(j => j.status === 'failed').length,
      activeJobs: this.activeJobs.size,
      maxConcurrentJobs: this.maxConcurrentJobs
    };
  }
}
