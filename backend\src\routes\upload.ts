import express from 'express';
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import axios from 'axios';

const router = express.Router();

// Configure multer for file uploads
const uploadDir = process.env.UPLOAD_DIR || './uploads';
const maxFileSize = parseInt(process.env.MAX_FILE_SIZE || '52428800'); // 50MB

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}_${file.originalname}`;
    cb(null, uniqueName);
  }
});

const fileFilter = (req: any, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Allowed file types
  const allowedTypes = [
    // Audio files
    'audio/mpeg', 'audio/mp3', 'audio/wav', 'audio/x-wav', 'audio/mp4', 'audio/m4a',
    // Text files
    'text/plain', 'text/txt',
    // Additional audio formats
    'audio/ogg', 'audio/webm', 'audio/flac'
  ];

  const allowedExtensions = ['.mp3', '.wav', '.m4a', '.txt', '.ogg', '.webm', '.flac'];
  const fileExtension = path.extname(file.originalname).toLowerCase();

  if (allowedTypes.includes(file.mimetype) || allowedExtensions.includes(fileExtension)) {
    cb(null, true);
  } else {
    cb(new Error(`Unsupported file type: ${file.mimetype}. Allowed types: ${allowedTypes.join(', ')}`));
  }
};

const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: maxFileSize
  }
});

// Upload file endpoint
router.post('/', upload.single('file'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        error: 'No file uploaded',
        message: 'Please select a file to upload.'
      });
    }

    const fileInfo = {
      id: uuidv4(),
      filename: req.file.filename,
      originalName: req.file.originalname,
      mimetype: req.file.mimetype,
      size: req.file.size,
      path: req.file.path,
      uploadedAt: new Date().toISOString()
    };

    console.log(`File uploaded: ${fileInfo.originalName} (${fileInfo.size} bytes)`);

    res.json({
      success: true,
      file: fileInfo
    });

  } catch (error) {
    console.error('Upload error:', error);
    res.status(500).json({
      error: 'Upload failed',
      message: error instanceof Error ? error.message : 'Unknown error occurred'
    });
  }
});

// Validate URL endpoint
router.post('/validate-url', async (req, res) => {
  try {
    const { url } = req.body;

    if (!url || typeof url !== 'string') {
      return res.status(400).json({
        success: false,
        error: 'Invalid URL',
        message: 'Please provide a valid URL.'
      });
    }

    // Basic URL validation
    let parsedUrl;
    try {
      parsedUrl = new URL(url);
    } catch {
      return res.status(400).json({
        success: false,
        error: 'Invalid URL format',
        message: 'The provided URL is not valid.'
      });
    }

    // Only allow HTTP/HTTPS
    if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
      return res.status(400).json({
        success: false,
        error: 'Unsupported protocol',
        message: 'Only HTTP and HTTPS URLs are supported.'
      });
    }

    // Make HEAD request to validate URL and get metadata
    try {
      const response = await axios.head(url, {
        timeout: 10000,
        maxRedirects: 5,
        headers: {
          'User-Agent': 'Content-Generator/1.0'
        }
      });

      const contentType = response.headers['content-type'] || '';
      const contentLength = response.headers['content-length'];
      
      // Check if it's an audio file
      const isAudio = contentType.startsWith('audio/') || 
                     ['.mp3', '.wav', '.m4a', '.ogg', '.webm', '.flac'].some(ext => 
                       url.toLowerCase().includes(ext));

      if (!isAudio) {
        return res.status(400).json({
          success: false,
          error: 'Invalid file type',
          message: 'The URL does not point to a supported audio file.'
        });
      }

      // Check file size if available
      if (contentLength && parseInt(contentLength) > maxFileSize) {
        return res.status(400).json({
          success: false,
          error: 'File too large',
          message: `The file is too large. Maximum size is ${Math.round(maxFileSize / 1024 / 1024)}MB.`
        });
      }

      res.json({
        success: true,
        metadata: {
          url,
          contentType,
          contentLength: contentLength ? parseInt(contentLength) : null,
          isAudio: true
        }
      });

    } catch (error: any) {
      console.error('URL validation error:', error.message);
      
      if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        return res.status(400).json({
          success: false,
          error: 'URL not accessible',
          message: 'The provided URL could not be accessed.'
        });
      }

      if (error.response?.status === 404) {
        return res.status(400).json({
          success: false,
          error: 'File not found',
          message: 'The file at the provided URL was not found.'
        });
      }

      return res.status(400).json({
        success: false,
        error: 'URL validation failed',
        message: 'Could not validate the provided URL.'
      });
    }

  } catch (error) {
    console.error('Validate URL error:', error);
    res.status(500).json({
      success: false,
      error: 'Validation failed',
      message: 'An error occurred while validating the URL.'
    });
  }
});

// Get uploaded files list
router.get('/files', (req, res) => {
  try {
    if (!fs.existsSync(uploadDir)) {
      return res.json({ files: [] });
    }

    const files = fs.readdirSync(uploadDir)
      .filter(filename => {
        const filePath = path.join(uploadDir, filename);
        return fs.statSync(filePath).isFile();
      })
      .map(filename => {
        const filePath = path.join(uploadDir, filename);
        const stats = fs.statSync(filePath);
        
        return {
          filename,
          size: stats.size,
          uploadedAt: stats.birthtime.toISOString(),
          path: `/uploads/${filename}`
        };
      })
      .sort((a, b) => new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime());

    res.json({ files });

  } catch (error) {
    console.error('Get files error:', error);
    res.status(500).json({
      error: 'Failed to get files',
      message: 'Could not retrieve uploaded files list.'
    });
  }
});

// Delete uploaded file
router.delete('/files/:filename', (req, res) => {
  try {
    const { filename } = req.params;
    
    if (!filename || filename.includes('..') || filename.includes('/')) {
      return res.status(400).json({
        error: 'Invalid filename',
        message: 'The provided filename is not valid.'
      });
    }

    const filePath = path.join(uploadDir, filename);
    
    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        error: 'File not found',
        message: 'The specified file was not found.'
      });
    }

    fs.unlinkSync(filePath);
    console.log(`File deleted: ${filename}`);

    res.json({
      success: true,
      message: 'File deleted successfully.'
    });

  } catch (error) {
    console.error('Delete file error:', error);
    res.status(500).json({
      error: 'Delete failed',
      message: 'Could not delete the file.'
    });
  }
});

export default router;
