import express from 'express';
import { JobManager } from '../services/jobManager';
import { GenerationParams } from '../../../shared/types';

export default function createProcessRoutes(jobManager: JobManager) {
  const router = express.Router();

  // Start processing endpoint
  router.post('/start', async (req, res) => {
    try {
      const { inputType, inputData, params } = req.body;

      // Validate input
      if (!inputType || !inputData || !params) {
        return res.status(400).json({
          error: 'Missing required fields',
          message: 'inputType, inputData, and params are required.'
        });
      }

      if (!['file', 'url'].includes(inputType)) {
        return res.status(400).json({
          error: 'Invalid input type',
          message: 'inputType must be either "file" or "url".'
        });
      }

      // Validate parameters
      const validatedParams: GenerationParams = {
        vocabularyLevel: params.vocabularyLevel || 1500,
        translateToChinese: <PERSON><PERSON><PERSON>(params.translateToChinese),
        generateImages: Boolean(params.generateImages),
        imageStyle: params.imageStyle || 'flat',
        speechSpeed: params.speechSpeed || 'normal',
        voiceType: params.voiceType || 'harry',
        outputResolution: params.outputResolution || '1024x1024'
      };

      // Validate vocabulary level
      if (validatedParams.vocabularyLevel < 500 || validatedParams.vocabularyLevel > 5000) {
        return res.status(400).json({
          error: 'Invalid vocabulary level',
          message: 'Vocabulary level must be between 500 and 5000.'
        });
      }

      // Validate image style
      if (!['flat', 'realistic', 'cartoon'].includes(validatedParams.imageStyle)) {
        return res.status(400).json({
          error: 'Invalid image style',
          message: 'Image style must be one of: flat, realistic, cartoon.'
        });
      }

      // Validate speech speed
      if (!['slow', 'normal', 'fast'].includes(validatedParams.speechSpeed)) {
        return res.status(400).json({
          error: 'Invalid speech speed',
          message: 'Speech speed must be one of: slow, normal, fast.'
        });
      }

      console.log(`Starting processing job: ${inputType}:${inputData}`);

      // Create and start job
      const job = await jobManager.createJob(inputType, inputData, validatedParams);

      return res.json({
        success: true,
        jobId: job.id,
        status: job.status,
        message: 'Processing job created successfully.'
      });

    } catch (error) {
      console.error('Start processing error:', error);
      return res.status(500).json({
        error: 'Failed to start processing',
        message: error instanceof Error ? error.message : 'Unknown error occurred'
      });
    }
  });

  // Get processing status endpoint
  router.get('/status/:jobId', (req, res) => {
    try {
      const { jobId } = req.params;

      if (!jobId) {
        return res.status(400).json({
          error: 'Missing job ID',
          message: 'Job ID is required.'
        });
      }

      const job = jobManager.getJob(jobId);

      if (!job) {
        return res.status(404).json({
          error: 'Job not found',
          message: 'The specified job was not found.'
        });
      }

      return res.json({
        success: true,
        job
      });

    } catch (error) {
      console.error('Get status error:', error);
      return res.status(500).json({
        error: 'Failed to get status',
        message: 'Could not retrieve job status.'
      });
    }
  });

  // Cancel processing endpoint
  router.post('/cancel/:jobId', async (req, res) => {
    try {
      const { jobId } = req.params;

      if (!jobId) {
        return res.status(400).json({
          error: 'Missing job ID',
          message: 'Job ID is required.'
        });
      }

      const success = await jobManager.cancelJob(jobId);

      if (!success) {
        return res.status(404).json({
          error: 'Job not found',
          message: 'The specified job was not found.'
        });
      }

      return res.json({
        success: true,
        message: 'Job cancelled successfully.'
      });

    } catch (error) {
      console.error('Cancel job error:', error);
      return res.status(500).json({
        error: 'Failed to cancel job',
        message: 'Could not cancel the job.'
      });
    }
  });

  // Restart processing endpoint
  router.post('/restart/:jobId', async (req, res) => {
    try {
      const { jobId } = req.params;

      if (!jobId) {
        return res.status(400).json({
          error: 'Missing job ID',
          message: 'Job ID is required.'
        });
      }

      const newJobId = await jobManager.restartJob(jobId);

      return res.json({
        success: true,
        jobId: newJobId,
        status: 'pending',
        message: 'Job restarted successfully.'
      });

    } catch (error) {
      console.error('Restart job error:', error);

      if (error instanceof Error && error.message === 'Job not found') {
        return res.status(404).json({
          error: 'Job not found',
          message: 'The specified job was not found.'
        });
      }

      return res.status(500).json({
        error: 'Failed to restart job',
        message: 'Could not restart the job.'
      });
    }
  });

  return router;
}
