// Development server starter with better error handling
import dotenv from 'dotenv';
import path from 'path';
import fs from 'fs';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env') });

console.log('🚀 Starting Content Generator Backend...\n');

// Check environment
console.log('Environment checks:');
console.log(`- Node.js version: ${process.version}`);
console.log(`- Environment: ${process.env.NODE_ENV || 'development'}`);
console.log(`- Port: ${process.env.PORT || 3001}`);

// Check OpenAI API key
if (!process.env.OPENAI_API_KEY) {
  console.log('❌ OPENAI_API_KEY not found!');
  console.log('   Please create a .env file in the project root with your OpenAI API key:');
  console.log('   OPENAI_API_KEY=your_api_key_here\n');
  process.exit(1);
} else {
  console.log('✅ OpenAI API key found');
}

// Check directories
const uploadDir = process.env.UPLOAD_DIR || './uploads';
const outputDir = process.env.OUTPUT_DIR || './outputs';

[uploadDir, outputDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`✅ Created directory: ${dir}`);
  } else {
    console.log(`✅ Directory exists: ${dir}`);
  }
});

console.log('\n📡 Starting server...\n');

// Start the main server
import('./index').catch(error => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});
