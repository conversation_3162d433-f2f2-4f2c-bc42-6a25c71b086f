# 内容生成器 v1.0

一个基于AI的智能内容生成器，支持音频转录、文本简化、多语言翻译、图像生成、语音合成和视频制作。

## 功能特性

### 核心功能
- **音频转录**: 使用 OpenAI Whisper 将音频文件转换为文本
- **文本简化**: 根据词汇水平简化英文文本
- **多语言翻译**: 支持英文到中文的智能翻译
- **图像生成**: 使用 DALL-E 根据文本内容生成相关图片
- **语音合成**: 使用 OpenAI TTS 生成高质量语音
- **字幕生成**: 自动生成 SRT 格式字幕文件
- **视频合成**: 使用 FFmpeg 将音频、图片和字幕合成为视频

### 输入支持
- **文件上传**: 支持 MP3、WAV、M4A 音频文件和 TXT 文本文件
- **URL链接**: 支持直接从网络URL获取音频文件
- **实时处理**: WebSocket 实时显示处理进度

### 参数配置
- **词汇等级**: 500-5000词，适应不同学习水平
- **翻译选项**: 可选择是否生成中文翻译
- **图像风格**: 扁平插画、写实风格、卡通风格
- **语音选项**: 多种语音类型和语速设置
- **输出格式**: 多种分辨率选择

## 技术栈

### 后端
- **Node.js + TypeScript**: 服务器端开发
- **Express**: Web 框架
- **OpenAI API**: AI服务集成
  - GPT-4: 文本简化和翻译
  - DALL-E-3: 图像生成
  - Whisper: 音频转录
  - TTS: 语音合成
- **FFmpeg**: 音视频处理
- **WebSocket**: 实时通信

### 前端
- **React + TypeScript**: 用户界面开发
- **Ant Design**: UI组件库
- **Tailwind CSS**: 样式框架
- **Vite**: 构建工具

### 部署
- **Docker**: 容器化部署
- **Nginx**: 反向代理和静态文件服务

## 快速开始

### 环境要求
- Node.js 18+
- Docker & Docker Compose
- OpenAI API Key

### 1. 克隆项目
\`\`\`bash
git clone <repository-url>
cd content-generator
\`\`\`

### 2. 环境配置
\`\`\`bash
# 复制环境变量文件
cp .env.example .env

# 编辑环境变量，添加 OpenAI API Key
nano .env
\`\`\`

必需的环境变量：
\`\`\`env
OPENAI_API_KEY=your_openai_api_key_here
\`\`\`

### 3. Docker 部署（推荐）

#### 生产环境
\`\`\`bash
# 构建和启动所有服务
docker-compose up -d

# 查看日志
docker-compose logs -f
\`\`\`

#### 开发环境
\`\`\`bash
# 启动后端服务
docker-compose -f docker-compose.dev.yml up -d

# 在本地运行前端（默认 5174 端口）
cd frontend
npm install
npm run dev
\`\`\`

### 4. 本地开发

#### 安装依赖
\`\`\`bash
# 安装所有依赖
npm run install:all
\`\`\`

#### 启动服务
\`\`\`bash
# 同时启动前后端
npm run dev

# 或分别启动
npm run dev:backend  # 后端: http://localhost:3001
npm run dev:frontend # 前端: http://localhost:5173
\`\`\`

## 使用说明

### 1. 选择输入方式
- **文件上传**: 拖拽或点击上传音频/文本文件
- **URL输入**: 输入公开可访问的音频文件URL

### 2. 配置生成参数
- **词汇等级**: 选择适合的词汇水平（500-5000词）
- **翻译设置**: 决定是否需要中文翻译
- **图像生成**: 选择是否生成配图及风格
- **语音设置**: 选择发音人和语速
- **输出格式**: 选择视频分辨率

### 3. 开始生成
点击"开始生成"按钮，系统将自动处理：
1. 音频转录（如果是音频输入）
2. 文本简化
3. 中文翻译（如果启用）
4. 生成语音
5. 生成图像（如果启用）
6. 创建字幕
7. 合成视频

### 4. 查看结果
- **视频文件**: 最终生成的MP4视频
- **音频文件**: 生成的语音文件
- **字幕文件**: SRT格式字幕
- **图片文件**: 生成的相关图片
- **文本内容**: 简化文本和翻译结果

## API 文档

### 文件上传
\`\`\`
POST /api/upload
Content-Type: multipart/form-data

Body: file (音频或文本文件)
\`\`\`

### URL验证
\`\`\`
POST /api/upload/validate-url
Content-Type: application/json

Body: { "url": "https://example.com/audio.mp3" }
\`\`\`

### 开始处理
\`\`\`
POST /api/process/start
Content-Type: application/json

Body: {
  "inputType": "file|url",
  "inputData": "file_path_or_url",
  "params": {
    "vocabularyLevel": 1500,
    "translateToChinese": true,
    "generateImages": false,
    "imageStyle": "flat",
    "speechSpeed": "normal",
    "voiceType": "harry",
    "outputResolution": "1024x1024"
  }
}
\`\`\`

### 查看状态
\`\`\`
GET /api/process/status/{jobId}
\`\`\`

### WebSocket 连接
\`\`\`
ws://localhost:3001

// 订阅任务更新
send: { "type": "subscribe", "jobId": "job_id" }

// 接收更新
receive: { "type": "job_update", "jobId": "job_id", "data": {...} }
\`\`\`

## 项目结构

\`\`\`
content-generator/
├── backend/                 # 后端代码
│   ├── src/
│   │   ├── routes/         # API 路由
│   │   ├── services/       # 业务逻辑
│   │   └── index.ts        # 入口文件
│   └── package.json
├── frontend/               # 前端代码
│   ├── src/
│   │   ├── components/     # React 组件
│   │   ├── services/       # API 服务
│   │   └── types/          # 类型定义
│   └── package.json
├── shared/                 # 共享类型定义
├── docker-compose.yml      # Docker 配置
└── README.md
\`\`\`

## 配置说明

### 环境变量

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| OPENAI_API_KEY | OpenAI API密钥 | 必需 |
| PORT | 后端服务端口 | 3001 |
| NODE_ENV | 运行环境 | development |
| UPLOAD_DIR | 上传文件目录 | ./uploads |
| OUTPUT_DIR | 输出文件目录 | ./outputs |
| MAX_FILE_SIZE | 最大文件大小 | 52428800 (50MB) |
| CORS_ORIGIN | 跨域来源 | http://localhost:5174 |
| MAX_CONCURRENT_JOBS | 最大并发任务数 | 3 |

### FFmpeg 要求
系统需要安装 FFmpeg 用于音视频处理。Docker 镜像已包含 FFmpeg。

## 故障排除

### 常见问题

1. **OpenAI API 错误**
   - 检查 API Key 是否正确
   - 确认账户余额充足
   - 检查网络连接

2. **文件上传失败**
   - 检查文件大小是否超出限制
   - 确认文件格式支持
   - 检查磁盘空间

3. **视频生成失败**
   - 确认 FFmpeg 已正确安装
   - 检查临时目录权限
   - 查看后端日志

4. **WebSocket 连接失败**
   - 检查防火墙设置
   - 确认端口未被占用
   - 检查代理配置

### 日志查看
\`\`\`bash
# Docker 环境
docker-compose logs -f backend
docker-compose logs -f frontend

# 本地开发
# 后端日志会在控制台显示
# 前端日志在浏览器开发者工具
\`\`\`

## 性能优化

- 建议为大文件处理增加更多内存
- 可以通过 MAX_CONCURRENT_JOBS 控制并发数量
- 定期清理临时文件和旧任务记录
- 考虑使用 Redis 进行任务队列管理（高级功能）

## 安全考虑

- 确保 OpenAI API Key 安全存储
- 定期清理上传的敏感文件
- 设置适当的文件大小限制
- 考虑添加用户认证（生产环境）

## 许可证

[MIT License](LICENSE)

## 贡献指南

欢迎提交 Issue 和 Pull Request！

## 更新日志

### v1.0.0
- 初始版本发布
- 支持音频转录、文本简化、翻译、图像生成
- 完整的视频合成流水线
- Docker 部署支持