// Jest setup file
import dotenv from 'dotenv';

// Load test environment variables
dotenv.config({ path: '.env.test' });

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock environment variables for tests
process.env.NODE_ENV = 'test';
process.env.UPLOAD_DIR = './test-uploads';
process.env.OUTPUT_DIR = './test-outputs';
process.env.MAX_CONCURRENT_JOBS = '1';

// Global test timeout
jest.setTimeout(30000);
