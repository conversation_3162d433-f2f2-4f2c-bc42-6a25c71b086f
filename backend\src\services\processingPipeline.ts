import path from 'path';
import fs from 'fs';
import { ProcessingJob, GenerationParams } from '../../../shared/types';
import { OpenAIService } from './openai';
import { SubtitleService } from './subtitle';
import { VideoService } from './video';

export interface PipelineCallbacks {
  onProgress: (progress: number) => void;
  onStepUpdate: (stepName: string, stepData: any) => void;
  onComplete: (result: any) => void;
  onError: (error: string) => void;
}

export class ProcessingPipeline {
  private openaiService: OpenAIService;
  private subtitleService: SubtitleService;
  private videoService: VideoService;
  private cancelled = false;

  constructor(
    private job: ProcessingJob,
    private callbacks: PipelineCallbacks
  ) {
    this.openaiService = new OpenAIService();
    this.subtitleService = new SubtitleService();
    this.videoService = new VideoService();
  }

  async start(): Promise<void> {
    try {
      console.log(`Starting processing pipeline for job ${this.job.id}`);
      
      // Step 1: Prepare input (5%)
      this.updateProgress(5, 'Preparing input...');
      const inputPath = await this.prepareInput();
      
      if (this.cancelled) return;

      // Step 2: Transcribe audio (20%)
      this.updateProgress(20, 'Transcribing audio...');
      const transcription = await this.transcribeAudio(inputPath);
      
      if (this.cancelled) return;

      // Step 3: Simplify text (35%)
      this.updateProgress(35, 'Simplifying text...');
      const simplifiedText = await this.simplifyText(transcription.text);
      
      if (this.cancelled) return;

      // Step 4: Translate text (50%)
      let translation;
      if (this.job.params.translateToChinese) {
        this.updateProgress(50, 'Translating to Chinese...');
        translation = await this.translateText(simplifiedText.simplified);
      }
      
      if (this.cancelled) return;

      // Step 5: Generate speech (65%)
      this.updateProgress(65, 'Generating speech...');
      const generatedAudio = await this.generateSpeech(simplifiedText.simplified);
      
      if (this.cancelled) return;

      // Step 6: Generate images (80%)
      let generatedImages;
      if (this.job.params.generateImages) {
        this.updateProgress(80, 'Generating images...');
        generatedImages = await this.generateImages(simplifiedText.simplified);
      }
      
      if (this.cancelled) return;

      // Step 7: Generate subtitles (90%)
      this.updateProgress(90, 'Generating subtitles...');
      const subtitleUrl = await this.generateSubtitles(transcription, translation, simplifiedText.simplified);
      
      if (this.cancelled) return;

      // Step 8: Generate video (95%)
      this.updateProgress(95, 'Generating video...');
      const videoUrl = await this.generateVideo(generatedAudio, generatedImages, subtitleUrl);
      
      if (this.cancelled) return;

      // Complete (100%)
      this.updateProgress(100, 'Processing complete!');
      
      const result = {
        videoUrl,
        subtitleUrl,
        audioUrl: generatedAudio.audioUrl,
        images: generatedImages?.map(img => img.url) || [],
        transcription: transcription.text,
        simplifiedText: simplifiedText.simplified,
        translation: translation?.chinese,
        duration: generatedAudio.duration
      };

      this.callbacks.onComplete(result);

    } catch (error) {
      console.error(`Pipeline error for job ${this.job.id}:`, error);
      this.callbacks.onError(error instanceof Error ? error.message : 'Unknown error');
    }
  }

  async cancel(): Promise<void> {
    console.log(`Cancelling pipeline for job ${this.job.id}`);
    this.cancelled = true;
  }

  private updateProgress(progress: number, message: string) {
    console.log(`Job ${this.job.id}: ${progress}% - ${message}`);
    this.callbacks.onProgress(progress);
    this.callbacks.onStepUpdate('progress', { progress, message });
  }

  private async prepareInput(): Promise<string> {
    if (this.job.inputType === 'file') {
      // Input is a file path
      const uploadDir = process.env.UPLOAD_DIR || './uploads';
      const filePath = path.join(uploadDir, this.job.inputData);
      
      if (!fs.existsSync(filePath)) {
        throw new Error(`Input file not found: ${this.job.inputData}`);
      }

      // Check if it's a text file
      const ext = path.extname(filePath).toLowerCase();
      if (ext === '.txt') {
        // For text files, we need to generate speech first
        const textContent = fs.readFileSync(filePath, 'utf-8');
        const generatedAudio = await this.openaiService.generateSpeech(
          textContent,
          this.job.params.voiceType,
          this.getSpeechSpeed(this.job.params.speechSpeed)
        );
        
        // Return the path to the generated audio file
        return path.join(process.cwd(), generatedAudio.audioUrl.replace('/outputs/', process.env.OUTPUT_DIR || './outputs') + '/');
      }

      return filePath;
    } else {
      // Input is a URL - download the audio
      return await this.openaiService.downloadAudioFromUrl(this.job.inputData);
    }
  }

  private async transcribeAudio(audioPath: string) {
    this.callbacks.onStepUpdate('transcription', { status: 'starting' });
    
    const transcription = await this.openaiService.transcribeAudio(audioPath);
    
    this.callbacks.onStepUpdate('transcription', {
      status: 'completed',
      text: transcription.text,
      segmentCount: transcription.segments.length
    });

    return transcription;
  }

  private async simplifyText(text: string) {
    this.callbacks.onStepUpdate('simplification', { status: 'starting' });
    
    const simplified = await this.openaiService.simplifyText(text, this.job.params.vocabularyLevel);
    
    this.callbacks.onStepUpdate('simplification', {
      status: 'completed',
      originalLength: text.length,
      simplifiedLength: simplified.simplified.length,
      vocabularyLevel: this.job.params.vocabularyLevel
    });

    return simplified;
  }

  private async translateText(text: string) {
    this.callbacks.onStepUpdate('translation', { status: 'starting' });
    
    const translation = await this.openaiService.translateText(text);
    
    this.callbacks.onStepUpdate('translation', {
      status: 'completed',
      englishLength: translation.english.length,
      chineseLength: translation.chinese.length
    });

    return translation;
  }

  private async generateSpeech(text: string) {
    this.callbacks.onStepUpdate('speech', { status: 'starting' });
    
    const speech = await this.openaiService.generateSpeech(
      text,
      this.job.params.voiceType,
      this.getSpeechSpeed(this.job.params.speechSpeed)
    );
    
    this.callbacks.onStepUpdate('speech', {
      status: 'completed',
      duration: speech.duration,
      voice: this.job.params.voiceType,
      speed: this.job.params.speechSpeed
    });

    return speech;
  }

  private async generateImages(text: string) {
    this.callbacks.onStepUpdate('images', { status: 'starting' });
    
    // Extract keywords and generate image prompts
    const keywords = await this.openaiService.extractKeywords(text);
    
    if (keywords.imagePrompts.length === 0) {
      this.callbacks.onStepUpdate('images', {
        status: 'skipped',
        reason: 'No suitable image prompts found'
      });
      return undefined;
    }

    // Generate images
    const images = await this.openaiService.generateImages(
      keywords.imagePrompts,
      this.job.params.imageStyle
    );
    
    this.callbacks.onStepUpdate('images', {
      status: 'completed',
      imageCount: images.length,
      style: this.job.params.imageStyle,
      keywords: keywords.keywords
    });

    return images;
  }

  private async generateSubtitles(transcription: any, translation?: any, simplifiedText?: string) {
    this.callbacks.onStepUpdate('subtitles', { status: 'starting' });
    
    const subtitleUrl = await this.subtitleService.generateSubtitles(
      transcription,
      translation,
      simplifiedText
    );
    
    this.callbacks.onStepUpdate('subtitles', {
      status: 'completed',
      hasTranslation: !!translation,
      segmentCount: transcription.segments.length
    });

    return subtitleUrl;
  }

  private async generateVideo(audio: any, images?: any[], subtitleUrl?: string) {
    this.callbacks.onStepUpdate('video', { status: 'starting' });
    
    const audioPath = path.join(process.cwd(), audio.audioUrl.replace('/outputs/', process.env.OUTPUT_DIR || './outputs') + '/');
    
    const videoUrl = await this.videoService.generateVideo({
      audioPath,
      images,
      subtitlePath: subtitleUrl,
      outputResolution: this.job.params.outputResolution,
      fps: 25,
      imageDuration: images ? Math.max(3, audio.duration / images.length) : undefined
    });
    
    this.callbacks.onStepUpdate('video', {
      status: 'completed',
      hasImages: !!images && images.length > 0,
      hasSubtitles: !!subtitleUrl,
      resolution: this.job.params.outputResolution,
      duration: audio.duration
    });

    return videoUrl;
  }

  private getSpeechSpeed(speed: string): number {
    switch (speed) {
      case 'slow': return 0.8;
      case 'fast': return 1.2;
      default: return 1.0;
    }
  }
}
