# Development Dockerfile for backend
FROM node:18-alpine

# Install FFmpeg and curl for health checks
RUN apk add --no-cache ffmpeg curl

# Create app directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY tsconfig.json ./
COPY nodemon.json ./

# Install all dependencies (including dev dependencies)
RUN npm install

# Create directories for uploads and outputs
RUN mkdir -p uploads outputs

# Expose port
EXPOSE 3001

# Start the application in development mode
CMD ["npm", "run", "dev"]
