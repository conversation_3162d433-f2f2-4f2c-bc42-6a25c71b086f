import React, { useState, useRef } from 'react';
import { Tabs, Button, Input, Alert, message } from 'antd';
import { UploadOutlined, LinkOutlined, FileTextOutlined, AudioOutlined } from '@ant-design/icons';
import { uploadAPI } from '../services/api';
import type { UploadedFile } from '../types';

const { TabPane } = Tabs;
// const { TextArea } = Input;

interface InputSectionProps {
  onStartProcessing: (inputType: 'file' | 'url', inputData: string) => void;
  disabled?: boolean;
}

const InputSection: React.FC<InputSectionProps> = ({ onStartProcessing, disabled }) => {
  const [activeTab, setActiveTab] = useState<'upload' | 'url'>('upload');
  const [uploading, setUploading] = useState(false);
  const [uploadedFile, setUploadedFile] = useState<UploadedFile | null>(null);
  const [urlInput, setUrlInput] = useState('');
  const [urlValidating, setUrlValidating] = useState(false);
  const [urlValid, setUrlValid] = useState<boolean | null>(null);
  const [dragOver, setDragOver] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 文件上传处理
  const handleFileUpload = async (file: File) => {
    setUploading(true);
    try {
      const uploadedFile = await uploadAPI.uploadFile(file);
      setUploadedFile(uploadedFile);
      message.success('文件上传成功！');
    } catch (error: any) {
      message.error(`上传失败: ${error.response?.data?.error || error.message}`);
    } finally {
      setUploading(false);
    }
  };

  // URL验证
  const handleUrlValidation = async (url: string) => {
    if (!url.trim()) {
      setUrlValid(null);
      return;
    }

    setUrlValidating(true);
    try {
      const result = await uploadAPI.validateUrl(url);
      setUrlValid(result.success);
      if (!result.success) {
        message.error(result.error || 'URL验证失败');
      }
    } catch (error: any) {
      setUrlValid(false);
      message.error(`URL验证失败: ${error.response?.data?.error || error.message}`);
    } finally {
      setUrlValidating(false);
    }
  };

  // 拖拽处理
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileUpload(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  };

  // 开始处理
  const handleStart = () => {
    if (activeTab === 'upload' && uploadedFile) {
      onStartProcessing('file', uploadedFile.filename);
    } else if (activeTab === 'url' && urlInput && urlValid) {
      onStartProcessing('url', urlInput);
    }
  };

  const canStart = (activeTab === 'upload' && uploadedFile) || (activeTab === 'url' && urlInput && urlValid);

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">内容生成器 v1.0</h2>
        <p className="text-gray-600">通过音频文件或文本生成配音、图片和视频内容</p>
      </div>

      {/* 步骤1: 选择音频输入方式 */}
      <div className="parameter-card p-6">
        <h3 className="text-lg font-semibold text-gray-800 mb-4">1. 选择音频输入方式</h3>
        
        <Tabs
          activeKey={activeTab}
          onChange={(key) => setActiveTab(key as 'upload' | 'url')}
          className="custom-tabs"
        >
          <TabPane
            tab={
              <span className="flex items-center space-x-2">
                <UploadOutlined />
                <span>上传文件</span>
              </span>
            }
            key="upload"
          >
            <div
              className={`upload-area p-8 text-center ${dragOver ? 'dragover' : ''}`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onClick={() => fileInputRef.current?.click()}
              style={{ cursor: 'pointer' }}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept=".mp3,.wav,.m4a,.txt"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) handleFileUpload(file);
                }}
                style={{ display: 'none' }}
              />
              
              {uploading ? (
                <div className="space-y-2">
                  <div className="text-blue-500">上传中...</div>
                </div>
              ) : uploadedFile ? (
                <div className="space-y-3">
                  <div className="text-green-600 text-lg">
                    {uploadedFile.mimetype.startsWith('audio/') ? <AudioOutlined /> : <FileTextOutlined />}
                  </div>
                  <div className="text-gray-800 font-medium">{uploadedFile.originalName}</div>
                  <div className="text-gray-500 text-sm">
                    {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                  </div>
                  <Button
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      setUploadedFile(null);
                    }}
                  >
                    重新选择
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  <div className="text-4xl text-blue-500">
                    <UploadOutlined />
                  </div>
                  <div>
                    <div className="text-gray-700 font-medium">拖拽文件到此处，或点击选择</div>
                    <div className="text-gray-500 text-sm mt-2">
                      支持：音频文件（wav, mp3）/ 文本文件（txt, text）
                    </div>
                    <div className="text-gray-400 text-xs mt-1">
                      最大文件大小：50MB
                    </div>
                  </div>
                </div>
              )}
            </div>
          </TabPane>

          <TabPane
            tab={
              <span className="flex items-center space-x-2">
                <LinkOutlined />
                <span>输入URL</span>
              </span>
            }
            key="url"
          >
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  音频URL地址
                </label>
                <Input
                  placeholder="https://example.com/audio.mp3"
                  value={urlInput}
                  onChange={(e) => {
                    setUrlInput(e.target.value);
                    setUrlValid(null);
                  }}
                  onBlur={() => handleUrlValidation(urlInput)}
                  status={urlValid === false ? 'error' : ''}
                  suffix={urlValidating ? <div className="text-blue-500">验证中...</div> : null}
                />
              </div>

              <Alert
                type="info"
                showIcon
                message="URL输入说明："
                description={
                  <ul className="list-disc list-inside space-y-1 text-sm">
                    <li>支持直接访问的音频文件URL</li>
                    <li>支持MP3、WAV等常见音频格式</li>
                    <li>URL必须是公开可访问的</li>
                    <li>单文件大小不超过50MB</li>
                  </ul>
                }
                className="url-tips"
              />
            </div>
          </TabPane>
        </Tabs>
      </div>

      {/* 开始生成按钮 */}
      <div className="flex justify-center space-x-4">
        <Button
          type="primary"
          size="large"
          className="generate-button px-8 py-2 h-auto"
          onClick={handleStart}
          disabled={!canStart || disabled}
          icon={<UploadOutlined />}
        >
          开始生成
        </Button>
        
        <Button
          size="large"
          className="clear-button px-6 py-2 h-auto"
          onClick={() => {
            setUploadedFile(null);
            setUrlInput('');
            setUrlValid(null);
          }}
        >
          清空设置
        </Button>
      </div>
    </div>
  );
};

export default InputSection;