# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Build outputs
dist/
build/
*.tsbuildinfo

# Runtime files
uploads/
outputs/
logs/
temp/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Docker
.dockerignore

# Package managers
package-lock.json
yarn.lock
pnpm-lock.yaml

# Testing
coverage/
.nyc_output

# Misc
*.log
*.pid
*.seed
*.pid.lock