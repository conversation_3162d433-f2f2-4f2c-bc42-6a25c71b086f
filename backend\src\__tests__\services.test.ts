import { SubtitleService } from '../services/subtitle';
import { WebSocketManager } from '../services/websocket';
import { JobManager } from '../services/jobManager';
import { WebSocketServer } from 'ws';

// Mock WebSocket Server for testing
const mockWss = {
  on: jest.fn(),
  clients: new Set()
} as any;

describe('SubtitleService', () => {
  let subtitleService: SubtitleService;

  beforeEach(() => {
    subtitleService = new SubtitleService();
  });

  test('should format time correctly', () => {
    // Access private method through any cast for testing
    const formatTime = (subtitleService as any).formatTime;
    
    expect(formatTime(0)).toBe('00:00:00,000');
    expect(formatTime(65.5)).toBe('00:01:05,500');
    expect(formatTime(3661.123)).toBe('01:01:01,123');
  });

  test('should estimate text duration', () => {
    const estimateTextDuration = (subtitleService as any).estimateTextDuration;
    
    const shortText = 'Hello world';
    const duration = estimateTextDuration(shortText);
    expect(duration).toBeGreaterThan(0);
  });

  test('should create subtitle entries from transcription', () => {
    const mockTranscription = {
      text: 'Hello world. This is a test.',
      segments: [
        { start: 0, end: 2, text: 'Hello world.' },
        { start: 2, end: 5, text: 'This is a test.' }
      ]
    };

    const createSubtitleEntries = (subtitleService as any).createSubtitleEntries;
    const entries = createSubtitleEntries(mockTranscription);

    expect(entries).toHaveLength(2);
    expect(entries[0].englishText).toBe('Hello world.');
    expect(entries[1].englishText).toBe('This is a test.');
  });
});

describe('WebSocketManager', () => {
  let wsManager: WebSocketManager;

  beforeEach(() => {
    wsManager = new WebSocketManager(mockWss);
  });

  afterEach(() => {
    wsManager.destroy();
  });

  test('should initialize with zero connected clients', () => {
    expect(wsManager.getConnectedClientsCount()).toBe(0);
  });

  test('should return zero subscribers for non-existent job', () => {
    expect(wsManager.getJobSubscribersCount('non-existent-job')).toBe(0);
  });
});

describe('JobManager', () => {
  let jobManager: JobManager;
  let wsManager: WebSocketManager;

  beforeEach(() => {
    wsManager = new WebSocketManager(mockWss);
    jobManager = new JobManager(wsManager);
  });

  afterEach(() => {
    wsManager.destroy();
  });

  test('should initialize with zero active jobs', () => {
    expect(jobManager.getActiveJobsCount()).toBe(0);
  });

  test('should create job with correct parameters', async () => {
    const params = {
      vocabularyLevel: 1500,
      translateToChinese: true,
      generateImages: false,
      imageStyle: 'flat' as const,
      speechSpeed: 'normal' as const,
      voiceType: 'harry',
      outputResolution: '1920x1080'
    };

    const job = await jobManager.createJob('file', 'test.mp3', params);

    expect(job.id).toBeDefined();
    expect(job.status).toBe('pending');
    expect(job.inputType).toBe('file');
    expect(job.inputData).toBe('test.mp3');
    expect(job.params).toEqual(params);
  });

  test('should get job stats correctly', () => {
    const stats = jobManager.getJobStats();
    
    expect(stats).toHaveProperty('total');
    expect(stats).toHaveProperty('pending');
    expect(stats).toHaveProperty('processing');
    expect(stats).toHaveProperty('completed');
    expect(stats).toHaveProperty('failed');
    expect(stats).toHaveProperty('activeJobs');
    expect(stats).toHaveProperty('maxConcurrentJobs');
  });

  test('should return undefined for non-existent job', () => {
    const job = jobManager.getJob('non-existent-id');
    expect(job).toBeUndefined();
  });
});
