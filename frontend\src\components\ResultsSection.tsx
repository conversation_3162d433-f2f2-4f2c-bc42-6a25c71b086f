import React, { useState } from 'react';
import { <PERSON>, Button, Row, Col, Tag, Descriptions, Image, message, Modal } from 'antd';
import { ArrowLeftOutlined, DownloadOutlined, ReloadOutlined, PlayCircleOutlined, FileTextOutlined, SoundOutlined, PictureOutlined, ShareAltOutlined, EyeOutlined } from '@ant-design/icons';
import type { ProcessingJob } from '../types';

interface ResultsSectionProps {
  job: ProcessingJob;
  onBack: () => void;
  onRestart: () => void;
}

interface DownloadItem {
  type: 'video' | 'audio' | 'subtitle';
  title: string;
  description: string;
  url?: string;
  icon: string;
  color: string;
}

const ResultsSection: React.FC<ResultsSectionProps> = ({ job, onBack, onRestart }) => {
  const { result } = job;
  const [shareModal, setShareModal] = useState(false);

  const handleDownload = (url: string, filename: string) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    message.success(`开始下载 ${filename}`);
  };

  const formatDuration = (seconds?: number): string => {
    if (!seconds) return '6:12'; // 模拟时长
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 构建下载项目 - 参考图三的设计
  const downloadItems: DownloadItem[] = [
    {
      type: 'video',
      title: '视频文件',
      description: '含有音频、图片和字幕的完整视频',
      url: result?.videoUrl,
      icon: '📄',
      color: 'blue'
    },
    {
      type: 'audio', 
      title: '手写文件',
      description: '中英双语对照高清音频',
      url: result?.audioUrl,
      icon: '📄',
      color: 'green'
    },
    {
      type: 'subtitle',
      title: '英音音频',
      description: '英音双语音频朗读音频',
      url: result?.subtitleUrl,
      icon: '🔊',
      color: 'purple'
    }
  ];

  return (
    <div className="space-y-6 max-w-4xl mx-auto p-6">
      {/* 成功标题 */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-4">
          <span className="text-6xl mr-4">🎉</span>
          <h2 className="text-3xl font-bold text-green-600">内容生成完成！</h2>
        </div>
        <p className="text-gray-600 text-lg">您的内容已成功生成，可以预览和下载</p>
      </div>

      {/* 视频预览区域 - 参考图三 */}
      <Card className="mb-6">
        <div className="text-center">
          {result?.videoUrl ? (
            <div className="relative inline-block">
              <video 
                controls 
                className="w-full max-w-2xl rounded-lg shadow-lg"
                poster="/api/placeholder/800/450"
              >
                <source src={result.videoUrl} type="video/mp4" />
                您的浏览器不支持视频播放
              </video>
              <div className="absolute top-4 right-4 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                0:00 / {formatDuration(result?.duration)}
              </div>
            </div>
          ) : (
            // 占位图像 - 模拟图三的效果
            <div 
              className="relative inline-block w-full max-w-2xl h-96 rounded-lg shadow-lg flex items-center justify-center"
              style={{
                backgroundImage: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                backgroundSize: 'cover'
              }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-orange-200 to-yellow-100 rounded-lg opacity-80"></div>
              <div className="relative z-10 text-center">
                <div className="text-6xl mb-4">🎬</div>
                <div className="text-white text-xl font-semibold">视频预览</div>
                <div className="text-white text-sm opacity-80">AI生成的内容视频</div>
              </div>
              <div className="absolute bottom-4 left-4 right-4 bg-black bg-opacity-70 text-white p-3 rounded flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <PlayCircleOutlined className="text-2xl" />
                  <div className="w-32 h-1 bg-gray-400 rounded-full">
                    <div className="w-8 h-1 bg-white rounded-full"></div>
                  </div>
                  <span className="text-sm">0:00 / {formatDuration(result?.duration)}</span>
                </div>
                <div className="flex space-x-2">
                  <Button type="text" className="text-white" size="small">🔊</Button>
                  <Button type="text" className="text-white" size="small">⚙️</Button>
                  <Button type="text" className="text-white" size="small">⛶</Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* 下载选项卡片 - 参考图三 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        {downloadItems.map((item, index) => (
          <Card 
            key={index}
            className="text-center hover:shadow-lg transition-shadow duration-300"
            bodyStyle={{ padding: '24px 16px' }}
          >
            <div className="flex flex-col items-center space-y-3">
              <div className="text-4xl">{item.icon}</div>
              <div>
                <h3 className="font-semibold text-lg text-gray-800">{item.title}</h3>
                <p className="text-gray-500 text-sm">{item.description}</p>
              </div>
              <Button 
                type="primary"
                icon={<DownloadOutlined />}
                onClick={() => item.url && handleDownload(item.url, `${item.title}_${job.id}`)}
                className="w-full"
                style={{ 
                  backgroundColor: item.color === 'blue' ? '#1890ff' : 
                                  item.color === 'green' ? '#52c41a' : 
                                  '#722ed1',
                  borderColor: item.color === 'blue' ? '#1890ff' : 
                              item.color === 'green' ? '#52c41a' : 
                              '#722ed1'
                }}
              >
                下载 {item.title.slice(0, 2)}
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {/* 操作按钮 - 参考图三底部按钮 */}
      <div className="flex justify-center space-x-4 pt-6">
        <Button 
          size="large" 
          onClick={onRestart}
          className="px-8"
        >
          重新生成
        </Button>
        
        <Button 
          size="large"
          icon={<EyeOutlined />}
          onClick={() => {
            // 查看处理历史
            message.info('查看处理历史功能开发中...');
          }}
          className="px-8"
        >
          查看处理历史
        </Button>
        
        <Button 
          type="primary"
          size="large"
          icon={<ShareAltOutlined />}
          onClick={() => setShareModal(true)}
          className="px-8"
        >
          分享链接
        </Button>
      </div>

      {/* 详细信息展示 */}
      {(result?.simplifiedText || result?.translation) && (
        <Card title="📄 内容详情" className="mt-6">
          <div className="space-y-4">
            {result?.transcription && (
              <div>
                <h4 className="font-semibold text-gray-700 mb-2">原始文本</h4>
                <div className="bg-gray-50 p-4 rounded border text-sm">
                  {result.transcription}
                </div>
              </div>
            )}
            
            {result?.simplifiedText && (
              <div>
                <h4 className="font-semibold text-gray-700 mb-2">简化文本</h4>
                <div className="bg-blue-50 p-4 rounded border text-sm">
                  {result.simplifiedText}
                </div>
              </div>
            )}

            {result?.translation && (
              <div>
                <h4 className="font-semibold text-gray-700 mb-2">中文翻译</h4>
                <div className="bg-green-50 p-4 rounded border text-sm">
                  {result.translation}
                </div>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* 生成的图片展示 */}
      {result?.images && result.images.length > 0 && (
        <Card title="🎨 生成的图片" className="mt-6">
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {result.images.map((image, index) => (
              <div key={index} className="relative group">
                <div className="w-full h-32 bg-gradient-to-br from-blue-100 to-purple-100 rounded border hover:shadow-lg transition-shadow duration-300 flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-2xl mb-2">🖼️</div>
                    <div className="text-sm text-gray-600">Generated image {index + 1}</div>
                  </div>
                </div>
                <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded flex items-center justify-center">
                  <Button 
                    type="primary" 
                    icon={<DownloadOutlined />}
                    onClick={() => handleDownload(image, `generated_image_${index + 1}.jpg`)}
                  >
                    下载
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* 分享模态框 */}
      <Modal
        title="分享生成结果"
        open={shareModal}
        onCancel={() => setShareModal(false)}
        footer={[
          <Button key="cancel" onClick={() => setShareModal(false)}>
            取消
          </Button>,
          <Button key="copy" type="primary" onClick={() => {
            navigator.clipboard.writeText(window.location.href);
            message.success('链接已复制到剪贴板');
            setShareModal(false);
          }}>
            复制链接
          </Button>
        ]}
      >
        <div className="space-y-4">
          <div>
            <p className="text-gray-600 mb-2">分享链接：</p>
            <div className="bg-gray-50 p-3 rounded border break-all text-sm">
              {window.location.href}
            </div>
          </div>
          <div className="text-sm text-gray-500">
            * 链接有效期为7天，过期后将无法访问
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ResultsSection;