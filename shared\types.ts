export interface GenerationParams {
  vocabularyLevel: number;
  translateToChinese: boolean;
  generateImages: boolean;
  imageStyle: 'flat' | 'realistic' | 'cartoon';
  speechSpeed: 'slow' | 'normal' | 'fast';
  voiceType: string;
  outputResolution: string;
}

export interface ProcessingJob {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  inputType: 'file' | 'url';
  inputData: string;
  params: GenerationParams;
  progress: number;
  result?: {
    videoUrl?: string;
    subtitleUrl?: string;
    audioUrl?: string;
    images?: string[];
    transcription?: string;
    simplifiedText?: string;
    translation?: string;
    duration?: number;
  };
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProcessingStep {
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  result?: any;
  error?: string;
}

export interface AudioTranscription {
  text: string;
  segments: TranscriptionSegment[];
}

export interface TranscriptionSegment {
  start: number;
  end: number;
  text: string;
}

export interface SimplifiedText {
  original: string;
  simplified: string;
  vocabularyLevel: number;
}

export interface Translation {
  english: string;
  chinese: string;
}

export interface ExtractedKeywords {
  text: string;
  keywords: string[];
  imagePrompts: string[];
}

export interface GeneratedImage {
  prompt: string;
  url: string;
  segmentIndex: number;
}

export interface GeneratedAudio {
  text: string;
  audioUrl: string;
  duration: number;
}

export interface SubtitleEntry {
  index: number;
  startTime: string;
  endTime: string;
  englishText: string;
  chineseText?: string;
}