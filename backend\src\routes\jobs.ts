import express from 'express';
import { JobManager } from '../services/jobManager';

export default function createJobsRoutes(jobManager: JobManager) {
  const router = express.Router();

  // Get all jobs
  router.get('/', (req, res) => {
    try {
      const jobs = jobManager.getAllJobs();
      
      return res.json({
        success: true,
        jobs,
        count: jobs.length
      });

    } catch (error) {
      console.error('Get all jobs error:', error);
      return res.status(500).json({
        error: 'Failed to get jobs',
        message: 'Could not retrieve jobs list.'
      });
    }
  });

  // Get single job
  router.get('/:jobId', (req, res) => {
    try {
      const { jobId } = req.params;

      if (!jobId) {
        return res.status(400).json({
          error: 'Missing job ID',
          message: 'Job ID is required.'
        });
      }

      const job = jobManager.getJob(jobId);

      if (!job) {
        return res.status(404).json({
          error: 'Job not found',
          message: 'The specified job was not found.'
        });
      }

      return res.json({
        success: true,
        job
      });

    } catch (error) {
      console.error('Get job error:', error);
      return res.status(500).json({
        error: 'Failed to get job',
        message: 'Could not retrieve job details.'
      });
    }
  });

  // Delete job
  router.delete('/:jobId', (req, res) => {
    try {
      const { jobId } = req.params;

      if (!jobId) {
        return res.status(400).json({
          error: 'Missing job ID',
          message: 'Job ID is required.'
        });
      }

      const success = jobManager.deleteJob(jobId);

      if (!success) {
        return res.status(404).json({
          error: 'Job not found',
          message: 'The specified job was not found.'
        });
      }

      return res.json({
        success: true,
        message: 'Job deleted successfully.'
      });

    } catch (error) {
      console.error('Delete job error:', error);
      return res.status(500).json({
        error: 'Failed to delete job',
        message: 'Could not delete the job.'
      });
    }
  });

  // Get job statistics
  router.get('/stats/summary', (req, res) => {
    try {
      const stats = jobManager.getJobStats();
      
      return res.json({
        success: true,
        stats
      });

    } catch (error) {
      console.error('Get job stats error:', error);
      return res.status(500).json({
        error: 'Failed to get statistics',
        message: 'Could not retrieve job statistics.'
      });
    }
  });

  // Cleanup old jobs
  router.post('/cleanup', (req, res) => {
    try {
      const { olderThan } = req.body;
      
      // Parse olderThan parameter (default to 24 hours)
      let hours = 24;
      if (olderThan) {
        if (typeof olderThan === 'string') {
          // Parse strings like "2d", "12h", "30m"
          const match = olderThan.match(/^(\d+)([hdm])$/);
          if (match) {
            const value = parseInt(match[1]);
            const unit = match[2];
            
            switch (unit) {
              case 'd':
                hours = value * 24;
                break;
              case 'h':
                hours = value;
                break;
              case 'm':
                hours = value / 60;
                break;
            }
          } else {
            return res.status(400).json({
              error: 'Invalid olderThan format',
              message: 'Use format like "2d", "12h", or "30m".'
            });
          }
        } else if (typeof olderThan === 'number') {
          hours = olderThan;
        }
      }

      const deletedCount = jobManager.cleanupOldJobs(hours);

      return res.json({
        success: true,
        deletedCount,
        message: `Cleaned up ${deletedCount} jobs older than ${hours} hours.`
      });

    } catch (error) {
      console.error('Cleanup jobs error:', error);
      return res.status(500).json({
        error: 'Failed to cleanup jobs',
        message: 'Could not cleanup old jobs.'
      });
    }
  });

  return router;
}
