import { WebSocketServer, WebSocket } from 'ws';
import { v4 as uuidv4 } from 'uuid';

export interface WebSocketClient {
  id: string;
  ws: WebSocket;
  subscribedJobs: Set<string>;
  lastPing: number;
}

export interface WebSocketMessage {
  type: string;
  jobId?: string;
  data?: any;
}

export class WebSocketManager {
  private clients: Map<string, WebSocketClient> = new Map();
  private jobSubscriptions: Map<string, Set<string>> = new Map(); // jobId -> clientIds
  private pingInterval: NodeJS.Timeout;

  constructor(private wss: WebSocketServer) {
    this.setupWebSocketServer();
    this.startPingInterval();
  }

  private setupWebSocketServer() {
    this.wss.on('connection', (ws: WebSocket) => {
      const clientId = uuidv4();
      const client: WebSocketClient = {
        id: clientId,
        ws,
        subscribedJobs: new Set(),
        lastPing: Date.now()
      };

      this.clients.set(clientId, client);
      console.log(`WebSocket client connected: ${clientId}`);

      // Send welcome message
      this.sendToClient(clientId, {
        type: 'connected',
        data: { clientId }
      });

      // Handle messages
      ws.on('message', (data: Buffer) => {
        try {
          const message: WebSocketMessage = JSON.parse(data.toString());
          this.handleMessage(clientId, message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
          this.sendToClient(clientId, {
            type: 'error',
            data: { message: 'Invalid message format' }
          });
        }
      });

      // Handle pong responses
      ws.on('pong', () => {
        const client = this.clients.get(clientId);
        if (client) {
          client.lastPing = Date.now();
        }
      });

      // Handle disconnection
      ws.on('close', () => {
        this.handleDisconnection(clientId);
      });

      ws.on('error', (error) => {
        console.error(`WebSocket error for client ${clientId}:`, error);
        this.handleDisconnection(clientId);
      });
    });
  }

  private handleMessage(clientId: string, message: WebSocketMessage) {
    const client = this.clients.get(clientId);
    if (!client) return;

    switch (message.type) {
      case 'subscribe':
        if (message.jobId) {
          this.subscribeClientToJob(clientId, message.jobId);
        }
        break;

      case 'unsubscribe':
        if (message.jobId) {
          this.unsubscribeClientFromJob(clientId, message.jobId);
        }
        break;

      case 'ping':
        this.sendToClient(clientId, { type: 'pong' });
        break;

      default:
        console.warn(`Unknown message type: ${message.type}`);
    }
  }

  private subscribeClientToJob(clientId: string, jobId: string) {
    const client = this.clients.get(clientId);
    if (!client) return;

    // Add job to client's subscriptions
    client.subscribedJobs.add(jobId);

    // Add client to job's subscribers
    if (!this.jobSubscriptions.has(jobId)) {
      this.jobSubscriptions.set(jobId, new Set());
    }
    this.jobSubscriptions.get(jobId)!.add(clientId);

    console.log(`Client ${clientId} subscribed to job ${jobId}`);

    // Confirm subscription
    this.sendToClient(clientId, {
      type: 'subscribed',
      jobId,
      data: { success: true }
    });
  }

  private unsubscribeClientFromJob(clientId: string, jobId: string) {
    const client = this.clients.get(clientId);
    if (!client) return;

    // Remove job from client's subscriptions
    client.subscribedJobs.delete(jobId);

    // Remove client from job's subscribers
    const jobSubscribers = this.jobSubscriptions.get(jobId);
    if (jobSubscribers) {
      jobSubscribers.delete(clientId);
      if (jobSubscribers.size === 0) {
        this.jobSubscriptions.delete(jobId);
      }
    }

    console.log(`Client ${clientId} unsubscribed from job ${jobId}`);

    // Confirm unsubscription
    this.sendToClient(clientId, {
      type: 'unsubscribed',
      jobId,
      data: { success: true }
    });
  }

  private handleDisconnection(clientId: string) {
    const client = this.clients.get(clientId);
    if (!client) return;

    console.log(`WebSocket client disconnected: ${clientId}`);

    // Remove client from all job subscriptions
    client.subscribedJobs.forEach(jobId => {
      const jobSubscribers = this.jobSubscriptions.get(jobId);
      if (jobSubscribers) {
        jobSubscribers.delete(clientId);
        if (jobSubscribers.size === 0) {
          this.jobSubscriptions.delete(jobId);
        }
      }
    });

    // Remove client
    this.clients.delete(clientId);
  }

  private sendToClient(clientId: string, message: WebSocketMessage) {
    const client = this.clients.get(clientId);
    if (!client || client.ws.readyState !== WebSocket.OPEN) {
      return false;
    }

    try {
      client.ws.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error(`Error sending message to client ${clientId}:`, error);
      this.handleDisconnection(clientId);
      return false;
    }
  }

  // Public methods for broadcasting updates
  public broadcastJobUpdate(jobId: string, data: any) {
    const subscribers = this.jobSubscriptions.get(jobId);
    if (!subscribers || subscribers.size === 0) {
      return;
    }

    const message: WebSocketMessage = {
      type: 'job_update',
      jobId,
      data
    };

    let successCount = 0;
    subscribers.forEach(clientId => {
      if (this.sendToClient(clientId, message)) {
        successCount++;
      }
    });

    console.log(`Broadcasted job update for ${jobId} to ${successCount}/${subscribers.size} clients`);
  }

  public broadcastStepUpdate(jobId: string, stepName: string, stepData: any) {
    const subscribers = this.jobSubscriptions.get(jobId);
    if (!subscribers || subscribers.size === 0) {
      return;
    }

    const message: WebSocketMessage = {
      type: 'step_update',
      jobId,
      data: {
        stepName,
        ...stepData
      }
    };

    subscribers.forEach(clientId => {
      this.sendToClient(clientId, message);
    });
  }

  private startPingInterval() {
    this.pingInterval = setInterval(() => {
      const now = Date.now();
      const timeout = 30000; // 30 seconds

      this.clients.forEach((client, clientId) => {
        if (now - client.lastPing > timeout) {
          console.log(`Client ${clientId} timed out, disconnecting`);
          client.ws.terminate();
          this.handleDisconnection(clientId);
        } else if (client.ws.readyState === WebSocket.OPEN) {
          client.ws.ping();
        }
      });
    }, 15000); // Check every 15 seconds
  }

  public getConnectedClientsCount(): number {
    return this.clients.size;
  }

  public getJobSubscribersCount(jobId: string): number {
    return this.jobSubscriptions.get(jobId)?.size || 0;
  }

  public destroy() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
    }
    
    this.clients.forEach((client) => {
      client.ws.terminate();
    });
    
    this.clients.clear();
    this.jobSubscriptions.clear();
  }
}
