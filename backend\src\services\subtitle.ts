import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import { 
  AudioTranscription, 
  TranscriptionSegment, 
  Translation, 
  SubtitleEntry 
} from '../../../shared/types';

export class SubtitleService {
  private outputDir: string;

  constructor() {
    this.outputDir = process.env.OUTPUT_DIR || './outputs';
  }

  // Generate SRT subtitle file
  async generateSubtitles(
    transcription: AudioTranscription,
    translation?: Translation,
    simplifiedText?: string
  ): Promise<string> {
    try {
      console.log(`Generating subtitles for ${transcription.segments.length} segments`);

      const subtitleEntries = this.createSubtitleEntries(
        transcription,
        translation,
        simplifiedText
      );

      const srtContent = this.generateSRTContent(subtitleEntries);
      
      const filename = `subtitles_${uuidv4()}.srt`;
      const filepath = path.join(this.outputDir, filename);
      
      fs.writeFileSync(filepath, srtContent, 'utf-8');

      console.log(`Subtitles generated: ${filename}`);
      return `/outputs/${filename}`;

    } catch (error) {
      console.error('Subtitle generation error:', error);
      throw new Error(`Failed to generate subtitles: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private createSubtitleEntries(
    transcription: AudioTranscription,
    translation?: Translation,
    simplifiedText?: string
  ): SubtitleEntry[] {
    const entries: SubtitleEntry[] = [];

    // If we have segments from transcription, use them
    if (transcription.segments && transcription.segments.length > 0) {
      transcription.segments.forEach((segment, index) => {
        const englishText = simplifiedText 
          ? this.getSimplifiedSegmentText(segment.text, transcription.text, simplifiedText)
          : segment.text;

        const chineseText = translation 
          ? this.getTranslatedSegmentText(segment.text, transcription.text, translation.chinese)
          : undefined;

        entries.push({
          index: index + 1,
          startTime: this.formatTime(segment.start),
          endTime: this.formatTime(segment.end),
          englishText: englishText.trim(),
          chineseText: chineseText?.trim()
        });
      });
    } else {
      // Fallback: create single subtitle entry for entire text
      const duration = this.estimateTextDuration(transcription.text);
      
      entries.push({
        index: 1,
        startTime: this.formatTime(0),
        endTime: this.formatTime(duration),
        englishText: simplifiedText || transcription.text,
        chineseText: translation?.chinese
      });
    }

    return entries;
  }

  private getSimplifiedSegmentText(
    originalSegment: string,
    fullOriginalText: string,
    fullSimplifiedText: string
  ): string {
    // Try to find corresponding simplified text for this segment
    // This is a simple approach - in practice, you might want more sophisticated alignment
    
    const originalWords = originalSegment.trim().split(/\s+/);
    const fullOriginalWords = fullOriginalText.split(/\s+/);
    const fullSimplifiedWords = fullSimplifiedText.split(/\s+/);

    // Find the position of this segment in the full text
    const segmentStart = fullOriginalText.toLowerCase().indexOf(originalSegment.toLowerCase());
    
    if (segmentStart === -1) {
      return originalSegment; // Fallback to original if not found
    }

    // Calculate approximate position in simplified text
    const ratio = segmentStart / fullOriginalText.length;
    const simplifiedStart = Math.floor(ratio * fullSimplifiedText.length);
    
    // Extract corresponding portion from simplified text
    const segmentLength = originalSegment.length;
    const simplifiedSegment = fullSimplifiedText.substr(simplifiedStart, segmentLength * 1.2);
    
    // Clean up and return
    const sentences = simplifiedSegment.split(/[.!?]+/);
    return sentences[0]?.trim() || originalSegment;
  }

  private getTranslatedSegmentText(
    originalSegment: string,
    fullOriginalText: string,
    fullTranslation: string
  ): string {
    // Similar approach for translation
    const segmentStart = fullOriginalText.toLowerCase().indexOf(originalSegment.toLowerCase());
    
    if (segmentStart === -1) {
      return ''; // Return empty if not found
    }

    const ratio = segmentStart / fullOriginalText.length;
    const translationStart = Math.floor(ratio * fullTranslation.length);
    
    const segmentLength = originalSegment.length;
    const translatedSegment = fullTranslation.substr(translationStart, segmentLength);
    
    // Find sentence boundary
    const sentences = translatedSegment.split(/[。！？]+/);
    return sentences[0]?.trim() || '';
  }

  private formatTime(seconds: number): string {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    const milliseconds = Math.floor((seconds % 1) * 1000);

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${milliseconds.toString().padStart(3, '0')}`;
  }

  private generateSRTContent(entries: SubtitleEntry[]): string {
    let srtContent = '';

    entries.forEach(entry => {
      srtContent += `${entry.index}\n`;
      srtContent += `${entry.startTime} --> ${entry.endTime}\n`;
      
      // Add English text
      srtContent += entry.englishText;
      
      // Add Chinese text if available
      if (entry.chineseText) {
        srtContent += `\n${entry.chineseText}`;
      }
      
      srtContent += '\n\n';
    });

    return srtContent.trim();
  }

  private estimateTextDuration(text: string): number {
    // Estimate reading time: ~150 words per minute for English
    const wordCount = text.split(/\s+/).length;
    const readingTimeMinutes = wordCount / 150;
    return readingTimeMinutes * 60; // Convert to seconds
  }

  // Generate VTT subtitle file (alternative format)
  async generateVTTSubtitles(
    transcription: AudioTranscription,
    translation?: Translation,
    simplifiedText?: string
  ): Promise<string> {
    try {
      console.log(`Generating VTT subtitles for ${transcription.segments.length} segments`);

      const subtitleEntries = this.createSubtitleEntries(
        transcription,
        translation,
        simplifiedText
      );

      const vttContent = this.generateVTTContent(subtitleEntries);
      
      const filename = `subtitles_${uuidv4()}.vtt`;
      const filepath = path.join(this.outputDir, filename);
      
      fs.writeFileSync(filepath, vttContent, 'utf-8');

      console.log(`VTT subtitles generated: ${filename}`);
      return `/outputs/${filename}`;

    } catch (error) {
      console.error('VTT subtitle generation error:', error);
      throw new Error(`Failed to generate VTT subtitles: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private generateVTTContent(entries: SubtitleEntry[]): string {
    let vttContent = 'WEBVTT\n\n';

    entries.forEach(entry => {
      // Convert SRT time format to VTT format (replace comma with dot)
      const startTime = entry.startTime.replace(',', '.');
      const endTime = entry.endTime.replace(',', '.');
      
      vttContent += `${startTime} --> ${endTime}\n`;
      
      // Add English text
      vttContent += entry.englishText;
      
      // Add Chinese text if available
      if (entry.chineseText) {
        vttContent += `\n${entry.chineseText}`;
      }
      
      vttContent += '\n\n';
    });

    return vttContent.trim();
  }

  // Parse existing SRT file
  static parseSRTFile(filepath: string): SubtitleEntry[] {
    try {
      const content = fs.readFileSync(filepath, 'utf-8');
      const entries: SubtitleEntry[] = [];
      
      const blocks = content.trim().split('\n\n');
      
      blocks.forEach(block => {
        const lines = block.trim().split('\n');
        if (lines.length >= 3) {
          const index = parseInt(lines[0]);
          const timeLine = lines[1];
          const textLines = lines.slice(2);
          
          const [startTime, endTime] = timeLine.split(' --> ');
          
          // Separate English and Chinese text
          let englishText = '';
          let chineseText = '';
          
          textLines.forEach(line => {
            if (/[\u4e00-\u9fff]/.test(line)) {
              chineseText += (chineseText ? ' ' : '') + line;
            } else {
              englishText += (englishText ? ' ' : '') + line;
            }
          });
          
          entries.push({
            index,
            startTime,
            endTime,
            englishText: englishText.trim(),
            chineseText: chineseText.trim() || undefined
          });
        }
      });
      
      return entries;
    } catch (error) {
      console.error('Error parsing SRT file:', error);
      return [];
    }
  }
}
