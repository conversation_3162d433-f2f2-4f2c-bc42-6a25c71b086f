{"name": "content-generator-backend", "version": "1.0.0", "description": "Backend for AI-powered content generator", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec \"ts-node -r tsconfig-paths/register src/start-dev.ts\"", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:basic": "ts-node src/test.ts"}, "dependencies": {"axios": "^1.6.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "fluent-ffmpeg": "^2.1.2", "helmet": "^7.1.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "openai": "^4.20.1", "tsconfig-paths": "^4.2.0", "uuid": "^9.0.1", "ws": "^8.14.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/fluent-ffmpeg": "^2.1.24", "@types/jest": "^29.5.14", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/node-cron": "^3.0.11", "@types/uuid": "^9.0.7", "@types/ws": "^8.5.10", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-jest": "^29.4.1", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "keywords": ["ai", "content-generator", "openai", "video-processing"], "author": "", "license": "MIT"}