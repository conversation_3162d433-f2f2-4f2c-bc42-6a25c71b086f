import axios from 'axios';
import { GenerationParams, ProcessingJob, UploadedFile, JobStats } from '../types';

const API_BASE_URL = '/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000,
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log('API Request:', config.method?.toUpperCase(), config.url);
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    console.log('API Response:', response.status, response.config.url);
    return response;
  },
  (error) => {
    console.error('API Response Error:', error.response?.status, error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// 文件上传相关
export const uploadAPI = {
  // 上传文件
  uploadFile: async (file: File): Promise<UploadedFile> => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data.file;
  },

  // 验证URL
  validateUrl: async (url: string): Promise<{ success: boolean; metadata?: any; error?: string }> => {
    const response = await api.post('/upload/validate-url', { url });
    return response.data;
  },

  // 获取上传的文件列表
  getFiles: async (): Promise<UploadedFile[]> => {
    const response = await api.get('/upload/files');
    return response.data.files;
  },

  // 删除文件
  deleteFile: async (filename: string): Promise<void> => {
    await api.delete(`/upload/files/${filename}`);
  },
};

// 处理相关
export const processAPI = {
  // 开始处理
  startProcess: async (inputType: 'file' | 'url', inputData: string, params: GenerationParams): Promise<{ jobId: string; status: string }> => {
    const response = await api.post('/process/start', {
      inputType,
      inputData,
      params,
    });
    
    return response.data;
  },

  // 获取处理状态
  getStatus: async (jobId: string): Promise<ProcessingJob> => {
    const response = await api.get(`/process/status/${jobId}`);
    return response.data.job;
  },

  // 取消处理
  cancelProcess: async (jobId: string): Promise<void> => {
    await api.post(`/process/cancel/${jobId}`);
  },

  // 重新开始处理
  restartProcess: async (jobId: string): Promise<{ jobId: string; status: string }> => {
    const response = await api.post(`/process/restart/${jobId}`);
    return response.data;
  },
};

// 任务管理相关
export const jobsAPI = {
  // 获取所有任务
  getAllJobs: async (): Promise<ProcessingJob[]> => {
    const response = await api.get('/jobs');
    return response.data.jobs;
  },

  // 获取单个任务详情
  getJob: async (jobId: string): Promise<ProcessingJob> => {
    const response = await api.get(`/jobs/${jobId}`);
    return response.data.job;
  },

  // 删除任务
  deleteJob: async (jobId: string): Promise<void> => {
    await api.delete(`/jobs/${jobId}`);
  },

  // 获取任务统计
  getStats: async (): Promise<JobStats> => {
    const response = await api.get('/jobs/stats/summary');
    return response.data.stats;
  },

  // 清理任务
  cleanupJobs: async (olderThan?: string): Promise<{ deletedCount: number }> => {
    const response = await api.post('/jobs/cleanup', { olderThan });
    return response.data;
  },
};

// WebSocket连接
export class WebSocketManager {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private listeners: { [key: string]: ((data: any) => void)[] } = {};
  private lastSubscribedJobId: string | null = null;

  connect() {
    try {
      const wsUrl = `ws://${window.location.host}/ws`;
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.reconnectAttempts = 0;
        this.emit('connected', null);
        if (this.lastSubscribedJobId) {
          this.subscribeToJob(this.lastSubscribedJobId);
        }
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.emit(data.type, data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.emit('disconnected', null);
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.emit('error', error);
      };

    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      this.attemptReconnect();
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('Max reconnection attempts reached');
    }
  }

  subscribeToJob(jobId: string) {
    this.lastSubscribedJobId = jobId;
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({
        type: 'subscribe',
        jobId
      }));
    }
  }

  on(event: string, callback: (data: any) => void) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  off(event: string, callback: (data: any) => void) {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
  }

  private emit(event: string, data: any) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(data));
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.listeners = {};
  }
}

export const wsManager = new WebSocketManager();