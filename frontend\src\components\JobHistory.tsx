import React, { useState, useEffect } from 'react';
import { Table, Button, Tag, Space, message, Popconfirm, Tooltip } from 'antd';
import { EyeOutlined, DeleteOutlined, ReloadOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { jobsAPI } from '../services/api';
import type { ProcessingJob } from '../types';
import type { ColumnsType } from 'antd/es/table';

interface JobHistoryProps {
  onViewJob: (job: ProcessingJob) => void;
}

const JobHistory: React.FC<JobHistoryProps> = ({ onViewJob }) => {
  const [jobs, setJobs] = useState<ProcessingJob[]>([]);
  const [loading, setLoading] = useState(false);

  const loadJobs = async () => {
    setLoading(true);
    try {
      const jobList = await jobsAPI.getAllJobs();
      setJobs(jobList);
    } catch (error: any) {
      message.error(`获取任务列表失败: ${error.response?.data?.error || error.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadJobs();
  }, []);

  const handleDeleteJob = async (jobId: string) => {
    try {
      await jobsAPI.deleteJob(jobId);
      message.success('任务删除成功');
      loadJobs();
    } catch (error: any) {
      message.error(`删除任务失败: ${error.response?.data?.error || error.message}`);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'default';
      case 'processing':
        return 'processing';
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '排队中';
      case 'processing':
        return '处理中';
      case 'completed':
        return '已完成';
      case 'failed':
        return '失败';
      default:
        return status;
    }
  };

  const formatDuration = (startTime: Date, endTime: Date) => {
    const duration = new Date(endTime).getTime() - new Date(startTime).getTime();
    const seconds = Math.round(duration / 1000);
    
    if (seconds < 60) return `${seconds}秒`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}分钟`;
    return `${Math.round(seconds / 3600)}小时`;
  };

  const columns: ColumnsType<ProcessingJob> = [
    {
      title: '任务ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      render: (id: string) => (
        <Tooltip title={id}>
          <code className="text-xs bg-gray-100 px-1 rounded">
            {id.substring(0, 8)}...
          </code>
        </Tooltip>
      ),
    },
    {
      title: '输入类型',
      dataIndex: 'inputType',
      key: 'inputType',
      width: 80,
      render: (type: string) => (
        <Tag color={type === 'file' ? 'blue' : 'green'}>
          {type === 'file' ? '文件' : 'URL'}
        </Tag>
      ),
    },
    {
      title: '输入内容',
      dataIndex: 'inputData',
      key: 'inputData',
      ellipsis: true,
      render: (data: string) => (
        <Tooltip title={data}>
          <span className="text-sm text-gray-600">
            {data.length > 30 ? `${data.substring(0, 30)}...` : data}
          </span>
        </Tooltip>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string, record: ProcessingJob) => (
        <div className="space-y-1">
          <Tag color={getStatusColor(status)}>
            {getStatusText(status)}
          </Tag>
          {status === 'processing' && (
            <div className="text-xs text-gray-500">
              {record.progress}%
            </div>
          )}
        </div>
      ),
    },
    {
      title: '参数',
      key: 'params',
      width: 120,
      render: (record: ProcessingJob) => (
        <div className="text-xs text-gray-600 space-y-1">
          <div>{record.params.vocabularyLevel}词</div>
          <div>{record.params.translateToChinese ? '翻译' : '不翻译'}</div>
          <div>{record.params.generateImages ? '有图' : '无图'}</div>
        </div>
      ),
    },
    {
      title: '时间',
      key: 'time',
      width: 120,
      render: (record: ProcessingJob) => (
        <div className="text-xs text-gray-600 space-y-1">
          <div className="flex items-center">
            <ClockCircleOutlined className="mr-1" />
            {new Date(record.createdAt).toLocaleDateString()}
          </div>
          <div>
            {new Date(record.createdAt).toLocaleTimeString()}
          </div>
          {record.status === 'completed' && (
            <div className="text-green-600">
              用时: {formatDuration(record.createdAt, record.updatedAt)}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      render: (record: ProcessingJob) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => onViewJob(record)}
            />
          </Tooltip>
          
          <Popconfirm
            title="确定要删除这个任务吗？"
            onConfirm={() => handleDeleteJob(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除任务">
              <Button
                type="text"
                size="small"
                danger
                icon={<DeleteOutlined />}
                disabled={record.status === 'processing'}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="text-sm text-gray-600">
          共 {jobs.length} 个任务
        </div>
        <Button
          icon={<ReloadOutlined />}
          onClick={loadJobs}
          loading={loading}
          size="small"
        >
          刷新
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={jobs}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
          showQuickJumper: true,
          showTotal: (total, range) => `${range[0]}-${range[1]} 共 ${total} 条`,
        }}
        size="small"
        scroll={{ x: 800 }}
        className="bg-white rounded-lg"
      />

      {jobs.length === 0 && !loading && (
        <div className="text-center py-12 text-gray-500">
          <div className="text-lg mb-2">暂无任务记录</div>
          <div className="text-sm">开始创建您的第一个内容生成任务吧！</div>
        </div>
      )}
    </div>
  );
};

export default JobHistory;