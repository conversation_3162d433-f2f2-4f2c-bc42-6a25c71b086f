version: '3.8'

services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - UPLOAD_DIR=/app/uploads
      - OUTPUT_DIR=/app/outputs
      - CORS_ORIGIN=http://localhost:5173
    env_file:
      - .env
    volumes:
      - ./backend:/app
      - ./shared:/app/shared
      - /app/node_modules
      - ./backend/uploads:/app/uploads
      - ./backend/outputs:/app/outputs
    restart: unless-stopped
    command: npm run dev

volumes:
  uploads:
  outputs:
