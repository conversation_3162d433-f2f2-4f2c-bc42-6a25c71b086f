#!/bin/bash

# Content Generator Setup Script

set -e

echo "🚀 Setting up Content Generator..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    echo "❌ Node.js version 18+ is required. Current version: $(node -v)"
    exit 1
fi

echo "✅ Node.js $(node -v) detected"

# Check if Docker is installed (optional)
if command -v docker &> /dev/null; then
    echo "✅ Docker detected"
    DOCKER_AVAILABLE=true
else
    echo "⚠️  Docker not found. Docker deployment will not be available."
    DOCKER_AVAILABLE=false
fi

# Create environment file if it doesn't exist
if [ ! -f .env ]; then
    echo "📝 Creating .env file..."
    cp backend/.env.example .env
    echo "⚠️  Please edit .env file and add your OpenAI API key!"
else
    echo "✅ .env file already exists"
fi

# Install dependencies
echo "📦 Installing dependencies..."
npm run install:all

# Create necessary directories
echo "📁 Creating directories..."
mkdir -p backend/uploads
mkdir -p backend/outputs
mkdir -p logs

echo "✅ Setup completed!"
echo ""
echo "Next steps:"
echo "1. Edit .env file and add your OpenAI API key"
echo "2. Run 'npm run dev' to start development servers"
if [ "$DOCKER_AVAILABLE" = true ]; then
    echo "3. Or run 'npm run docker:dev' to start with Docker"
fi
echo ""
echo "For production deployment:"
if [ "$DOCKER_AVAILABLE" = true ]; then
    echo "- Run 'npm run docker:prod' to start with Docker"
fi
echo "- Or run 'npm run build && npm start' for local production"
