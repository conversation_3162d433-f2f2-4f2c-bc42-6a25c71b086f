import ffmpeg from 'fluent-ffmpeg';
import path from 'path';
import fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import { GeneratedImage } from '../../../shared/types';

export interface VideoGenerationOptions {
  audioPath: string;
  images?: GeneratedImage[];
  subtitlePath?: string;
  outputResolution?: string;
  fps?: number;
  imageDuration?: number;
}

export class VideoService {
  private outputDir: string;

  constructor() {
    this.outputDir = process.env.OUTPUT_DIR || './outputs';
  }

  // Generate video from audio, images, and subtitles
  async generateVideo(options: VideoGenerationOptions): Promise<string> {
    try {
      console.log('Starting video generation with options:', {
        audioPath: options.audioPath,
        imageCount: options.images?.length || 0,
        hasSubtitles: !!options.subtitlePath,
        resolution: options.outputResolution || '1920x1080'
      });

      const filename = `video_${uuidv4()}.mp4`;
      const outputPath = path.join(this.outputDir, filename);

      // Get audio duration
      const audioDuration = await this.getAudioDuration(options.audioPath);
      console.log(`Audio duration: ${audioDuration} seconds`);

      if (options.images && options.images.length > 0) {
        // Generate video with images
        await this.generateVideoWithImages(options, outputPath, audioDuration);
      } else {
        // Generate video with static background
        await this.generateVideoWithStaticBackground(options, outputPath, audioDuration);
      }

      console.log(`Video generated successfully: ${filename}`);
      return `/outputs/${filename}`;

    } catch (error) {
      console.error('Video generation error:', error);
      throw new Error(`Failed to generate video: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private async generateVideoWithImages(
    options: VideoGenerationOptions,
    outputPath: string,
    audioDuration: number
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const images = options.images!;
      const imageDuration = options.imageDuration || Math.max(3, audioDuration / images.length);
      
      // Create image list file for FFmpeg
      const imageListPath = path.join(this.outputDir, `imagelist_${uuidv4()}.txt`);
      const imageListContent = images.map(img => {
        const imagePath = path.join(process.cwd(), img.url.replace('/outputs/', this.outputDir + '/'));
        return `file '${imagePath}'\nduration ${imageDuration}`;
      }).join('\n') + `\nfile '${path.join(process.cwd(), images[images.length - 1].url.replace('/outputs/', this.outputDir + '/'))}'`;

      fs.writeFileSync(imageListPath, imageListContent);

      let command = ffmpeg()
        .input(imageListPath)
        .inputOptions(['-f', 'concat', '-safe', '0'])
        .input(options.audioPath)
        .outputOptions([
          '-c:v', 'libx264',
          '-c:a', 'aac',
          '-pix_fmt', 'yuv420p',
          '-shortest',
          '-r', (options.fps || 25).toString()
        ]);

      // Add resolution if specified
      if (options.outputResolution) {
        const [width, height] = options.outputResolution.split('x').map(Number);
        command = command.size(`${width}x${height}`);
      }

      // Add subtitles if provided
      if (options.subtitlePath) {
        const subtitleFullPath = path.join(process.cwd(), options.subtitlePath.replace('/outputs/', this.outputDir + '/'));
        command = command.outputOptions([
          '-vf', `subtitles='${subtitleFullPath}':force_style='FontSize=24,PrimaryColour=&Hffffff,OutlineColour=&H000000,Outline=2'`
        ]);
      }

      command
        .output(outputPath)
        .on('start', (commandLine) => {
          console.log('FFmpeg command:', commandLine);
        })
        .on('progress', (progress) => {
          console.log(`Video generation progress: ${progress.percent?.toFixed(1) || 0}%`);
        })
        .on('end', () => {
          // Clean up temporary files
          try {
            fs.unlinkSync(imageListPath);
          } catch (e) {
            console.warn('Could not delete temporary image list file:', e);
          }
          resolve();
        })
        .on('error', (error) => {
          // Clean up temporary files
          try {
            fs.unlinkSync(imageListPath);
          } catch (e) {
            console.warn('Could not delete temporary image list file:', e);
          }
          reject(error);
        })
        .run();
    });
  }

  private async generateVideoWithStaticBackground(
    options: VideoGenerationOptions,
    outputPath: string,
    audioDuration: number
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const [width, height] = (options.outputResolution || '1920x1080').split('x').map(Number);
      
      let command = ffmpeg()
        .input(`color=c=black:s=${width}x${height}:d=${audioDuration}`)
        .inputOptions(['-f', 'lavfi'])
        .input(options.audioPath)
        .outputOptions([
          '-c:v', 'libx264',
          '-c:a', 'aac',
          '-pix_fmt', 'yuv420p',
          '-shortest',
          '-r', (options.fps || 25).toString()
        ]);

      // Add subtitles if provided
      if (options.subtitlePath) {
        const subtitleFullPath = path.join(process.cwd(), options.subtitlePath.replace('/outputs/', this.outputDir + '/'));
        command = command.outputOptions([
          '-vf', `subtitles='${subtitleFullPath}':force_style='FontSize=32,PrimaryColour=&Hffffff,OutlineColour=&H000000,Outline=2,Alignment=2'`
        ]);
      }

      command
        .output(outputPath)
        .on('start', (commandLine) => {
          console.log('FFmpeg command:', commandLine);
        })
        .on('progress', (progress) => {
          console.log(`Video generation progress: ${progress.percent?.toFixed(1) || 0}%`);
        })
        .on('end', () => {
          resolve();
        })
        .on('error', (error) => {
          reject(error);
        })
        .run();
    });
  }

  private async getAudioDuration(audioPath: string): Promise<number> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(audioPath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }

        const duration = metadata.format.duration;
        if (typeof duration === 'number') {
          resolve(duration);
        } else {
          reject(new Error('Could not determine audio duration'));
        }
      });
    });
  }

  // Extract audio from video file
  async extractAudio(videoPath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const filename = `extracted_audio_${uuidv4()}.mp3`;
      const outputPath = path.join(this.outputDir, filename);

      ffmpeg(videoPath)
        .output(outputPath)
        .audioCodec('mp3')
        .noVideo()
        .on('start', (commandLine) => {
          console.log('FFmpeg audio extraction command:', commandLine);
        })
        .on('end', () => {
          console.log(`Audio extracted: ${filename}`);
          resolve(`/outputs/${filename}`);
        })
        .on('error', (error) => {
          console.error('Audio extraction error:', error);
          reject(error);
        })
        .run();
    });
  }

  // Convert audio format
  async convertAudio(inputPath: string, outputFormat: string = 'mp3'): Promise<string> {
    return new Promise((resolve, reject) => {
      const filename = `converted_audio_${uuidv4()}.${outputFormat}`;
      const outputPath = path.join(this.outputDir, filename);

      ffmpeg(inputPath)
        .output(outputPath)
        .audioCodec(outputFormat === 'mp3' ? 'mp3' : 'aac')
        .on('start', (commandLine) => {
          console.log('FFmpeg audio conversion command:', commandLine);
        })
        .on('end', () => {
          console.log(`Audio converted: ${filename}`);
          resolve(`/outputs/${filename}`);
        })
        .on('error', (error) => {
          console.error('Audio conversion error:', error);
          reject(error);
        })
        .run();
    });
  }

  // Get video/audio metadata
  async getMediaInfo(filePath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      ffmpeg.ffprobe(filePath, (err, metadata) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(metadata);
      });
    });
  }

  // Create thumbnail from video
  async createThumbnail(videoPath: string, timeOffset: number = 1): Promise<string> {
    return new Promise((resolve, reject) => {
      const filename = `thumbnail_${uuidv4()}.jpg`;
      const outputPath = path.join(this.outputDir, filename);

      ffmpeg(videoPath)
        .screenshots({
          timestamps: [timeOffset],
          filename: filename,
          folder: this.outputDir,
          size: '320x240'
        })
        .on('end', () => {
          console.log(`Thumbnail created: ${filename}`);
          resolve(`/outputs/${filename}`);
        })
        .on('error', (error) => {
          console.error('Thumbnail creation error:', error);
          reject(error);
        });
    });
  }
}
